name: twenty

services:
  change-vol-ownership:
    image: ubuntu
    user: root
    volumes:
      - server-local-data:/tmp/server-local-data
      - docker-data:/tmp/docker-data
    command: >
      bash -c "
      chown -R 1000:1000 /tmp/server-local-data
      && chown -R 1000:1000 /tmp/docker-data"

  server:
    # image: hondahatchq.azurecr.io/crm-server:${TAG:-latest}
    build:
      dockerfile: ./packages/twenty-docker/twenty/Dockerfile
    volumes:
      - server-local-data:/app/packages/twenty-server/${STORAGE_LOCAL_PATH:-.local-storage}
      - docker-data:/app/docker-data
    ports:
      - "3000:3000"
    environment:
      PORT: 3000
      PG_DATABASE_URL: postgres://${PG_DATABASE_USER:-postgres}:${PG_DATABASE_PASSWORD:-postgres}@${PG_DATABASE_HOST:-db}:${PG_DATABASE_PORT:-5432}/default
      SERVER_URL: ${SERVER_URL}
      REDIS_URL: ${REDIS_URL:-redis://redis:6379}
      DISABLE_DB_SEEDING: ${DISABLE_DB_SEEDING:-true}
      DISABLE_DB_WORKSPACE_MIGRATIONS: ${DISABLE_DB_WORKSPACE_MIGRATIONS:-true}

      STORAGE_TYPE: ${STORAGE_TYPE}
      STORAGE_S3_REGION: ${STORAGE_S3_REGION}
      STORAGE_S3_NAME: ${STORAGE_S3_NAME}
      STORAGE_S3_ENDPOINT: ${STORAGE_S3_ENDPOINT}

      STORAGE_AZURE_CONNECTION_STRING: ${STORAGE_AZURE_CONNECTION_STRING}
      STORAGE_AZURE_CONTAINER_NAME: ${STORAGE_AZURE_CRM_CONTAINER_NAME}

      APP_SECRET: ${APP_SECRET}
      HONDA_SERVER_BASE_URL: ${HONDA_SERVER_BASE_URL}
      VUE_APP_BASE_URL: ${VUE_APP_BASE_URL}

      IS_EMAIL_VERIFICATION_REQUIRED: ${IS_EMAIL_VERIFICATION_REQUIRED}
      EMAIL_VERIFICATION_TOKEN_EXPIRES_IN: ${EMAIL_VERIFICATION_TOKEN_EXPIRES_IN}
      EMAIL_FROM_ADDRESS: ${EMAIL_FROM_ADDRESS}
      EMAIL_SYSTEM_ADDRESS: ${EMAIL_SYSTEM_ADDRESS}
      EMAIL_FROM_NAME: ${EMAIL_FROM_NAME}
      EMAIL_DRIVER: ${EMAIL_DRIVER}
      EMAIL_SMTP_HOST: ${EMAIL_SMTP_HOST}
      EMAIL_SMTP_PORT: ${EMAIL_SMTP_PORT}
      EMAIL_SMTP_USER: ${EMAIL_SMTP_USER}
      EMAIL_SMTP_PASSWORD: ${EMAIL_SMTP_PASSWORD}
    depends_on:
      change-vol-ownership:
        condition: service_completed_successfully
      db:
        condition: service_healthy
    healthcheck:
      test: curl --fail http://**************:3000/healthz
      interval: 5s
      timeout: 5s
      retries: 10
    restart: always

  worker:
    # image: hondahatchq.azurecr.io/crm-server:${TAG:-latest}
    build:
      dockerfile: ./packages/twenty-docker/twenty/Dockerfile
    command: ["yarn", "worker:prod"]
    environment:
      PG_DATABASE_URL: postgres://${PG_DATABASE_USER:-postgres}:${PG_DATABASE_PASSWORD:-postgres}@${PG_DATABASE_HOST:-db}:${PG_DATABASE_PORT:-5432}/default
      SERVER_URL: ${SERVER_URL}
      REDIS_URL: ${REDIS_URL:-redis://redis:6379}
      DISABLE_DB_MIGRATIONS: "true" # it already runs on the server
      DISABLE_DB_SEEDING: "true"
      DISABLE_DB_WORKSPACE_MIGRATIONS: ${DISABLE_DB_WORKSPACE_MIGRATIONS:-true}


      STORAGE_TYPE: ${STORAGE_TYPE}
      STORAGE_S3_REGION: ${STORAGE_S3_REGION}
      STORAGE_S3_NAME: ${STORAGE_S3_NAME}
      STORAGE_S3_ENDPOINT: ${STORAGE_S3_ENDPOINT}

      STORAGE_AZURE_CONNECTION_STRING: ${STORAGE_AZURE_CONNECTION_STRING}
      STORAGE_AZURE_CONTAINER_NAME: ${STORAGE_AZURE_CRM_CONTAINER_NAME}

      APP_SECRET: ${APP_SECRET}
      HONDA_SERVER_BASE_URL: ${HONDA_SERVER_BASE_URL}

      IS_EMAIL_VERIFICATION_REQUIRED: ${IS_EMAIL_VERIFICATION_REQUIRED}
      EMAIL_VERIFICATION_TOKEN_EXPIRES_IN: ${EMAIL_VERIFICATION_TOKEN_EXPIRES_IN}
      EMAIL_FROM_ADDRESS: ${EMAIL_FROM_ADDRESS}
      EMAIL_SYSTEM_ADDRESS: ${EMAIL_SYSTEM_ADDRESS}
      EMAIL_FROM_NAME: ${EMAIL_FROM_NAME}
      EMAIL_DRIVER: ${EMAIL_DRIVER}
      EMAIL_SMTP_HOST: ${EMAIL_SMTP_HOST}
      EMAIL_SMTP_PORT: ${EMAIL_SMTP_PORT}
      EMAIL_SMTP_USER: ${EMAIL_SMTP_USER}
      EMAIL_SMTP_PASSWORD: ${EMAIL_SMTP_PASSWORD}
    depends_on:
      db:
        condition: service_healthy
      server:
        condition: service_healthy
    restart: always

  honda:
    # image: hondahatchq.azurecr.io/honda-server:${TAG:-latest}
    build:
      dockerfile: ./packages/honda-server/Dockerfile
    environment:
      APP_SECRET: ${APP_SECRET}
      PROJECT_NAME: "Honda Server API"
      FRONTEND_HOST: "http://server:3000"
      BACKEND_CORS_ORIGINS: "http://localhost,http://localhost:3000,https://localhost,https://localhost:3000"
      PG_DATABASE_URL: postgresql+asyncpg://${PG_DATABASE_USER:-postgres}:${PG_DATABASE_PASSWORD:-postgres}@${PG_DATABASE_HOST:-db}:${PG_DATABASE_PORT:-5432}/default
      REDIS_URL: ${REDIS_URL:-redis://redis:6379}
      STORAGE_AZURE_CONNECTION_STRING: ${STORAGE_AZURE_CONNECTION_STRING}
      STORAGE_AZURE_CONTAINER_NAME: ${STORAGE_AZURE_HONDA_CONTAINER_NAME}
    ports:
      - "8000:8000"
    restart: always
  db:
    image: postgres:16
    volumes:
      - db-data:/home/<USER>/pgdata
    environment:
      POSTGRES_USER: ${PG_DATABASE_USER:-postgres}
      POSTGRES_PASSWORD: ${PG_DATABASE_PASSWORD:-postgres}
    healthcheck:
      test: pg_isready -U ${PG_DATABASE_USER:-postgres} -h localhost -d postgres
      interval: 5s
      timeout: 5s
      retries: 10
    restart: always

  redis:
    image: redis
    restart: always

  form-client:
    # image: hondahatchq.azurecr.io/honda-form-client:${TAG:-latest}
    build:
      context: ./packages/form-client
      dockerfile: Dockerfile
    container_name: form-client
    environment:
      NODE_ENV: production
      NUXT_PUBLIC_APP_ENV: production
      HOST: "0.0.0.0"
      PORT: 3002
      # HMR settings
      CHOKIDAR_USEPOLLING: "true"
      WATCHPACK_POLLING: "true"
      VITE_HMR_HOST: "localhost"
      VITE_HMR_PORT: 24678
      # API settings
      NUXT_PUBLIC_APP_URL_BASE: "http://localhost:83"
      NUXT_PUBLIC_API_BASE: "http://localhost:83/api"
    extra_hosts:
      - "host.docker.internal:host-gateway"
    ports:
      - "3002:3002"      # Main dev server
      - "24678:24678"    # Vite HMR port
    restart: always
volumes:
  docker-data:
  db-data:
  server-local-data:
