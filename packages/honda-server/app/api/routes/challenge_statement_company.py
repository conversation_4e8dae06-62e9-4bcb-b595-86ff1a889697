"""Challenge Statement Company Routes"""

import json
import uuid
from typing import Annotated

from fastapi import APIRouter, Form, Query

from app.api.dto.challenge_statement_company import (
    ChallengeStatementCompanyCreateBulkRequest,
    ChallengeStatementCompanyCreateRequest,
    ChallengeStatementCompanyDeleteMultipleRequest,
    ChallengeStatementCompanyMoveRequest,
    ChallengeStatementCompanyUpdateRequest,
    ChallengeStatementCompanyUploadCSVRequest,
    ChallengeStatementCompanyUploadPDFRequest,
    ChallengeStatementCompanyUploadSolutionImageRequest,
)
from app.api.services.challenge_statement_company import challenge_statement_company_service
from app.core.deps import SessionDep, WorkspaceMemberDep
from app.core.exceptions import BadRequestException
from app.core.model_setup import get_all_model_dependencies
from app.core.config import settings
from app.api.services.twenty_file_service import twenty_file_service

router = APIRouter(
    prefix='/challenge-statement-company',
    tags=['challenge-statement-company'],
    dependencies=[
        *get_all_model_dependencies(),  # type: ignore[list-item]
    ],
)

bulk_router = APIRouter(
    prefix='/challenge-statement-companies',
    tags=['challenge-statement-company'],
    dependencies=[
        *get_all_model_dependencies(),  # type: ignore[list-item]
    ],
)


@router.post('/')
async def create_challenge_statement_company(
    request: ChallengeStatementCompanyCreateRequest, session: SessionDep, workspace_member_id: WorkspaceMemberDep
):
    """Create a challenge statement company"""
    return await challenge_statement_company_service.create_one_challenge_statement_company(
        request, session, workspace_member_id
    )


@bulk_router.post('/')
async def create_bulk_challenge_statement_company(
    request: ChallengeStatementCompanyCreateBulkRequest, session: SessionDep, workspace_member_id: WorkspaceMemberDep
):
    """Create multiple challenge statement companies"""
    return await challenge_statement_company_service.create_bulk_challenge_statement_companies(
        request, session, workspace_member_id
    )


@bulk_router.patch('/move')
async def move_challenge_statement_company(request: ChallengeStatementCompanyMoveRequest, session: SessionDep):
    """Move challenge statement companies to another challenge statement"""
    return await challenge_statement_company_service.move_challenge_statement_companies(request, session)


@router.post('/upload-pdf')
async def upload_pdf_challenge_statement_company(
    request: ChallengeStatementCompanyUploadPDFRequest, session: SessionDep
):
    """Upload challenge statement company PDF"""
    return await challenge_statement_company_service.upload_pdf(request, session)


@bulk_router.get('/')
async def retrieve_challenge_statement_companies(  # pylint: disable=too-many-arguments,too-many-positional-arguments
    session: SessionDep,
    page: int = 1,
    page_size: int = 100,
    challenge_statement_id: uuid.UUID | None = Query(
        None, description='Filter by challengeStatementId', alias='challengeStatementId'
    ),
    filters: str | None = Query(None, description='JSON string, e.g. \'{"name": "2024"}\'', alias='filter'),
    sort: str | None = Query(None, description='JSON string, e.g. \'[ ["createdAt", "desc"] ]\''),
    include_signed_urls: bool = Query(False, description='Include signed URLs for onePagerUrl fields'),
):
    """Get list of active challenge statement company records by challenge statement id"""
    try:
        filters_dict = json.loads(filters) if filters else None
    except Exception as exc:
        raise BadRequestException("Invalid JSON in 'filter' parameter") from exc
    try:
        sort_fields = json.loads(sort) if sort else None
    except Exception as exc:
        raise BadRequestException("Invalid JSON in 'sort' parameter") from exc

    return await challenge_statement_company_service.get_list_by_challenge_statement_id(
        challenge_statement_id, session, page, page_size, filters_dict, sort_fields, include_signed_urls
    )


@router.get('/{challenge_statement_company_id}')
async def retrieve_challenge_statement_company_by_id(
    challenge_statement_company_id: uuid.UUID,
    session: SessionDep,
    with_company_info: bool = False,
    include_signed_urls: bool = Query(False, description='Include signed URLs for file fields'),
):
    """Get challenge statement company by id"""
    return await challenge_statement_company_service.get_challenge_statement_company_by_id(
        challenge_statement_company_id, session, with_company_info, include_signed_urls
    )


@router.post('/upload-csv')
async def upload_csv_challenge_statement_company(
    request: Annotated[ChallengeStatementCompanyUploadCSVRequest, Form()],
    session: SessionDep,
    workspace_member_id: WorkspaceMemberDep,
):
    """Import Challenge Statement Company from CSV"""
    return await challenge_statement_company_service.upload_csv(
        request.challengeStatementId, session, request.file, workspace_member_id
    )


@router.post('/upload-solution-image')
async def upload_solution_image_challenge_statement_company(
    request: Annotated[ChallengeStatementCompanyUploadSolutionImageRequest, Form()],
    session: SessionDep,
):
    """Import Challenge Statement Company from CSV"""
    return await challenge_statement_company_service.upload_solution_image(
        request.challengeStatementCompanyId, session, request.image
    )


@router.patch('/{challenge_statement_company_id}')
async def update_challenge_statement_company(
    challenge_statement_company_id: uuid.UUID,
    request: ChallengeStatementCompanyUpdateRequest,
    session: SessionDep,
):
    """Update challenge statement company by id"""
    return await challenge_statement_company_service.update_one(challenge_statement_company_id, request, session)


@router.delete('/')
async def delete_multiple_challenge_statement_company(
    request: ChallengeStatementCompanyDeleteMultipleRequest,
    session: SessionDep,
):
    """Soft delete multiple challenge statement company"""
    return await challenge_statement_company_service.soft_delete_multiple(request.challengeStatementCompanies, session)


@router.delete('/{challenge_statement_company_id}')
async def delete_challenge_statement_company(
    challenge_statement_company_id: uuid.UUID,
    session: SessionDep,
):
    """Soft delete challenge statement company by id"""
    return await challenge_statement_company_service.soft_delete(challenge_statement_company_id, session)


@router.post('/generate-signed-urls')
async def generate_signed_urls_for_files(
    file_paths: list[str],
    container_name: str = settings.STORAGE_AZURE_CONTAINER_NAME,
    expires_in: str = '7d'
):
    """Generate signed URLs for multiple file paths"""
    files_to_sign = [
        {'filePath': file_path, 'containerName': container_name}
        for file_path in file_paths
    ]

    try:
        signed_urls = await twenty_file_service.generate_multiple_file_urls(
            files=files_to_sign,
            expires_in=expires_in
        )
        return {'signed_urls': signed_urls}
    except Exception as e:
        raise BadRequestException(f"Failed to generate signed URLs: {str(e)}")


@router.post('/form-generation')
async def get_companies_for_form_generation(
    challenge_statement_id: uuid.UUID,
    session: SessionDep,
    company_ids: list[uuid.UUID] = None
):
    """Get companies with 30-day signed URLs for form generation"""
    try:
        companies = await challenge_statement_company_service.get_companies_for_form_generation(
            challenge_statement_id, session, company_ids
        )
        return {'companies': companies}
    except Exception as e:
        raise BadRequestException(f"Failed to get companies for form generation: {str(e)}")
