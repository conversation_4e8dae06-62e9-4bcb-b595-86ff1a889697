"""Challenge statement company service"""

from base64 import b64decode
from datetime import UTC, datetime
from io import BytesIO
from typing import Any
from urllib.parse import urlparse
from uuid import UUID, uuid4

import pandas as pd
from fastapi import UploadFile
from pandas.errors import EmptyDataError
from sqlalchemy import func, select, update
from sqlalchemy.orm import selectinload
from sqlalchemy.sql.functions import count

from app.api.dto.challenge_statement_company import (
    ChallengeStatementCompanyCreateBulkRequest,
    ChallengeStatementCompanyCreateRequest,
    ChallengeStatementCompanyGetResponse,
    ChallengeStatementCompanyMoveRequest,
    ChallengeStatementCompanyUploadPDFRequest,
)
from app.api.services.base import BaseService
from app.core.config import settings
from app.core.deps import SessionDep
from app.core.exceptions import BadRequestException, DatabaseException, NotFoundException
from models.additional_info import AdditionalInfo
from models.challenge_cycle import ChallengeCycle
from models.challenge_statement import ChallengeStatement
from models.challenge_statement_company import ChallengeStatementCompanies
from models.company import Company, CompanyIndustryProfiles, CompanyNumberOfEmployeesMap, CompanyStages, Countries
from models.sensitive_info import SensitiveInfo
from utils.blob_storage import upload_file_to_blob
from utils.workspace import get_enum_value
from utils.workspace_member import add_actor_metadata
from app.api.services.twenty_file_service import twenty_file_service

class ChallengeStatementCompanyService(BaseService[ChallengeStatementCompanies]):
    """Challenge statement service"""

    def __init__(self):
        super().__init__(ChallengeStatementCompanies)

    async def _generate_signed_urls_for_record(
        self,
        record: ChallengeStatementCompanies,
        expires_in: str = "7d",
        container_name: str = "honda-pdfs"
    ) -> dict:
        """Generate signed URLs for file fields in a record"""
        record_dict = record.__dict__.copy()

        # Remove SQLAlchemy internal attributes
        record_dict.pop('_sa_instance_state', None)

        # Generate signed URLs for file fields
        files_to_sign = []

        if record.onePagerURL:
            files_to_sign.append({
                'filePath': record.onePagerURL,
                'containerName': container_name,
                'field': 'onePagerURL'
            })

        if record.solutionImage:
            files_to_sign.append({
                'filePath': record.solutionImage,
                'containerName': container_name,
                'field': 'solutionImage'
            })

        if files_to_sign:
            try:
                signed_urls = await twenty_file_service.generate_multiple_file_urls(
                    files=[{'filePath': f['filePath'], 'containerName': f['containerName']} for f in files_to_sign],
                    expires_in=expires_in
                )

                # Map signed URLs back to record
                for i, file_info in enumerate(files_to_sign):
                    field_name = file_info['field']
                    if i < len(signed_urls) and signed_urls[i].get('url'):
                        record_dict[f'{field_name}'] = signed_urls[i]['url']

            except Exception as e:
                # Log error but don't fail the entire request
                print(f"Failed to generate signed URLs: {e}")

        return record_dict

    async def get_companies_for_form_generation(
        self,
        challenge_statement_id: UUID,
        session: SessionDep,
        company_ids: list[UUID] = None
    ) -> list[dict]:
        """Get companies with 30-day signed URLs for form generation"""
        query = (
            select(
                ChallengeStatementCompanies,
                Company.name.label('companyName'),
            )
            .join(Company, ChallengeStatementCompanies.companyId == Company.id, isouter=True)
            .where(
                ChallengeStatementCompanies.deletedAt.is_(None),
                Company.deletedAt.is_(None),
                ChallengeStatementCompanies.challengeStatementId == challenge_statement_id
            )
        )

        # Filter by specific company IDs if provided
        if company_ids:
            query = query.where(ChallengeStatementCompanies.companyId.in_(company_ids))

        result = await session.execute(query)
        companies = result.scalars().all()

        # Generate 30-day signed URLs for form generation
        companies_with_urls = []
        for company in companies:
            company_dict = await self._generate_signed_urls_for_record(
                company,
                expires_in="30d",  # 30 days for form generation
                container_name=settings.STORAGE_AZURE_CONTAINER_NAME
            )
            companies_with_urls.append(company_dict)

        return companies_with_urls

    async def get_list_by_challenge_statement_id(  # pylint: disable=too-many-arguments,too-many-positional-arguments
        self,
        challenge_statement_id: UUID | None,
        session: SessionDep,
        page: int = 1,
        page_size: int = 100,
        filters: dict[str, Any] | None = None,
        sort: list[list[str]] | None = None,
        include_signed_urls: bool = True,
        expires_in: str = "7d",
    ) -> dict:
        """Get a list of active challenge statement company records by challenge statement id"""
        query = (
            select(
                ChallengeStatementCompanies,
                Company.name.label('companyName'),
            )
            .join(Company, ChallengeStatementCompanies.companyId == Company.id, isouter=True)
            .where(ChallengeStatementCompanies.deletedAt.is_(None), Company.deletedAt.is_(None))
        )
        count_query = (
            select(count(ChallengeStatementCompanies.id))
            .join(Company, ChallengeStatementCompanies.companyId == Company.id, isouter=True)
            .where(ChallengeStatementCompanies.deletedAt.is_(None), Company.deletedAt.is_(None))
        )
        if filters and 'companyName' in filters:
            company_name_filter = filters.pop('companyName')
            query = query.where(func.lower(Company.name).like(f'%{company_name_filter.lower()}%'))
            count_query = count_query.where(func.lower(Company.name).like(f'%{company_name_filter.lower()}%'))

        if challenge_statement_id:
            await BaseService(ChallengeStatement).get_by_id(challenge_statement_id, session)
            query = query.where(ChallengeStatementCompanies.challengeStatementId == challenge_statement_id)
            count_query = count_query.where(ChallengeStatementCompanies.challengeStatementId == challenge_statement_id)

        # Filtering
        query = self._apply_filters(query, filters)
        count_query = self._apply_filters(count_query, filters)

        # Sorting
        query = self._apply_sorting(query, sort)

        # Pagination
        query = self._apply_pagination(query, page, page_size)
        result = await session.execute(query)

        total_result = await session.execute(count_query)
        total = total_result.scalar() or 0

        items = result.scalars().all()

        print('----------------', items)

        # Generate signed URLs if requested
        if include_signed_urls:
            items_with_urls = []
            for item in items:
                item_dict = await self._generate_signed_urls_for_record(item, expires_in)
                items_with_urls.append(item_dict)
            items = items_with_urls

        return {
            'total': total,
            'page': page,
            'page_size': page_size,
            'items': items,
        }

    async def get_challenge_statement_company_by_id(
        self,
        challenge_statement_company_id: UUID,
        session: SessionDep,
        with_company_info: bool = False,
        include_signed_urls: bool = True,
        expires_in: str = "7d",
    ) -> ChallengeStatementCompanies | dict:
        """Get a list of active challenge statement company records by challenge statement id"""
        query = select(ChallengeStatementCompanies).where(
            ChallengeStatementCompanies.id == challenge_statement_company_id,
            ChallengeStatementCompanies.deletedAt.is_(None),
        )
        if with_company_info:
            query = query.options(selectinload(ChallengeStatementCompanies.company))
        result = await session.execute(query)
        record = result.scalars().first()
        if record is None:
            raise NotFoundException(ChallengeStatementCompanies.__name__, str(challenge_statement_company_id))
        challenge_statement_query = (
            select(ChallengeStatement)
            .where(
                ChallengeStatement.id == record.challengeStatementId,
                ChallengeStatement.deletedAt.is_(None),
            )
            .options(selectinload(ChallengeStatement.challengeCycle))
        )
        challenge_statement_query_result = await session.execute(challenge_statement_query)
        challenge_statement = challenge_statement_query_result.scalars().first()
        if challenge_statement is None:
            raise BadRequestException('Challenge statement of this Challenge statement company not found')
        record.statement = challenge_statement.statement  # type: ignore[attr-defined]
        record.challengeCycleId = challenge_statement.challengeCycleId  # type: ignore[attr-defined]
        record.challengeCycleName = (  # type: ignore[attr-defined]
            challenge_statement.challengeCycle.name if challenge_statement.challengeCycle else None
        )

        # Generate signed URLs if requested
        if include_signed_urls:
            return await self._generate_signed_urls_for_record(record, expires_in)

        return record

    async def create_one_challenge_statement_company(  # pylint: disable=too-many-locals
        self,
        request: ChallengeStatementCompanyCreateRequest,
        session: SessionDep,
        workspace_member_id: str,
    ) -> ChallengeStatementCompanyGetResponse:
        """Create a challenge statement company record"""
        challenge_statement = await BaseService(ChallengeStatement).get_by_id(request.challengeStatementId, session)

        workspace_member_data = await add_actor_metadata({}, workspace_member_id, session)
        recommended_by_data = await add_actor_metadata({}, workspace_member_id, session, key='recommendedBy')

        # Convert company data and handle URL fields
        company_data = request.company.model_dump()
        company_data['websiteLink'] = str(company_data['websiteLink'])
        if company_data.get('linkedinLink'):
            company_data['linkedinLink'] = str(company_data['linkedinLink'])

        # Find duplicate websiteLink
        existing_company_query = await session.execute(
            select(Company).where(Company.websiteLink == company_data['websiteLink'])
        )
        existing_company = existing_company_query.scalars().first()
        if existing_company:
            raise BadRequestException(f'A company with the website link {company_data["websiteLink"]} already exists.')
        try:
            # Get min position
            min_position_query = select(func.min(Company.position))
            result = await session.execute(min_position_query)
            min_position = result.scalar() or 0
            past_participation = {}
            if challenge_statement.challengeCycleId:
                challenge_cycle = await BaseService(ChallengeCycle).get_by_id(
                    challenge_statement.challengeCycleId,  # type: ignore[arg-type]
                    session,
                )
                past_participation[str(challenge_cycle.id)] = challenge_cycle.name
            company = Company(
                id=uuid4(),
                position=min_position,
                pastParticipation=past_participation,
                **company_data,
                **workspace_member_data,
            )
            session.add(company)

            # Additional Info and Sensitive Info
            session.add(
                AdditionalInfo(
                    id=uuid4(),
                    companyId=company.id,
                )
            )

            session.add(
                SensitiveInfo(
                    id=uuid4(),
                    companyId=company.id,
                )
            )

            challenge_statement_company = ChallengeStatementCompanies(
                id=uuid4(),
                companyId=company.id,
                challengeStatementId=request.challengeStatementId,
                description=None,
                analystInsight=None,
                pocPlan=None,
                solutionImage=None,
                recommendedCSList=[],
                productStage=None,
                onePagerURL=None,
                pastCycles=[],
                deletedAt=None,
                **recommended_by_data,
            )
            session.add(challenge_statement_company)
            await session.commit()
            return ChallengeStatementCompanyGetResponse.model_validate(
                {
                    **challenge_statement_company.__dict__,
                    'companyName': company.name,
                }
            )
        except Exception as exc:
            await session.rollback()
            raise DatabaseException(
                'create_challenge_statement_company', f'Failed to create companies: {str(exc)}'
            ) from exc

    async def create_bulk_challenge_statement_companies(  # pylint: disable=too-many-locals
        self,
        request: ChallengeStatementCompanyCreateBulkRequest,
        session: SessionDep,
        workspace_member_id: str,
    ) -> list[ChallengeStatementCompanyGetResponse]:
        """Create a challenge statement company record"""
        challenge_statement = await BaseService(ChallengeStatement).get_by_id(request.challengeStatementId, session)
        company_ids = request.companies
        companies = await BaseService(Company).get_by_ids(company_ids, session)

        # Check existing relationships
        existing_relationships_query = await session.execute(
            select(ChallengeStatementCompanies, Company.name.label('companyName'))
            .join(Company, ChallengeStatementCompanies.companyId == Company.id)
            .where(
                ChallengeStatementCompanies.challengeStatementId == request.challengeStatementId,
                ChallengeStatementCompanies.companyId.in_(company_ids),
                ChallengeStatementCompanies.deletedAt.is_(None),
            )
        )
        existing_relationships = existing_relationships_query.all()
        if existing_relationships:
            duplicate_companies = [f'{row.companyName}' for row in existing_relationships]
            raise BadRequestException(
                f'Companies already linked to this challenge statement: {", ".join(duplicate_companies)}'
            )
        try:
            recommended_by_data = await add_actor_metadata({}, workspace_member_id, session, key='recommendedBy')
            result = []
            for company in companies:
                past_participation: dict = (
                    company.pastParticipation if company.pastParticipation else {}  # type: ignore[assignment]
                )
                if challenge_statement.challengeCycleId:
                    challenge_cycle = await BaseService(ChallengeCycle).get_by_id(
                        challenge_statement.challengeCycleId,  # type: ignore[arg-type]
                        session,
                    )
                    past_participation[str(challenge_cycle.id)] = challenge_cycle.name
                challenge_statement_company = ChallengeStatementCompanies(
                    id=uuid4(),
                    companyId=company.id,
                    challengeStatementId=request.challengeStatementId,
                    description=None,
                    analystInsight=None,
                    pocPlan=None,
                    solutionImage=None,
                    recommendedCSList=[],
                    productStage=None,
                    onePagerURL=None,
                    pastCycles=[],
                    deletedAt=None,
                    createdAt=datetime.now(UTC),
                    updatedAt=datetime.now(UTC),
                    **recommended_by_data,
                )
                # Update company
                await session.execute(
                    update(Company)
                    .where(Company.id == company.id)
                    .values(pastParticipation=past_participation)  # type: ignore[arg-type]
                    .values(updatedAt=datetime.now(UTC))
                )
                session.add(challenge_statement_company)
                result.append(
                    ChallengeStatementCompanyGetResponse.model_validate(
                        {
                            **challenge_statement_company.__dict__,
                            'companyName': company.name,
                        }
                    )
                )
            await session.commit()
            return result
        except Exception as exc:
            await session.rollback()
            raise DatabaseException(
                'create_challenge_statement_company', f'Failed to create companies: {str(exc)}'
            ) from exc

    async def upload_pdf(
        self,
        request: ChallengeStatementCompanyUploadPDFRequest,
        session: SessionDep,
    ) -> ChallengeStatementCompanies:
        """Upload PDF and update record with blob URL"""
        # Get existing record
        query = (
            select(
                ChallengeStatementCompanies,
                ChallengeStatement.key.label('statement_key'),
                ChallengeCycle.name.label('cycle_name'),
            )
            .join(ChallengeStatement, ChallengeStatementCompanies.challengeStatementId == ChallengeStatement.id)
            .join(ChallengeCycle, ChallengeStatement.challengeCycleId == ChallengeCycle.id)
            .where(
                ChallengeStatementCompanies.id == request.challengeStatementCompanyId,
                ChallengeStatementCompanies.deletedAt.is_(None),
            )
        )
        result = await session.execute(query)
        row = result.first()  # Get the full row instead of just first column
        if row is None:
            raise NotFoundException(ChallengeStatementCompanies.__name__, str(request.challengeStatementCompanyId))
        try:
            # Unpack the row values
            record, statement_key, cycle_name, company_name = row

            # Create file path
            formatted_statement_key = f'CS-{str(int(statement_key)).zfill(2)}'
            file_path = f'{cycle_name}/{formatted_statement_key}/{company_name}/{request.fileName}'

            # Upload to blob storage
            pdf_content = b64decode(request.pdfData.pdfBase64)
            blob_url = await upload_file_to_blob(pdf_content, file_path)

            # Update record with URL
            record.onePagerURL = blob_url
            await session.commit()

            return record

        except Exception as exc:
            await session.rollback()
            raise DatabaseException('upload_pdf', f'Failed to upload PDF: {str(exc)}') from exc

    async def upload_csv(  # pylint: disable=too-many-locals
        self, challenge_statement_id: UUID, session: SessionDep, file: UploadFile, workspace_member_id: str
    ) -> str:
        """Process CSV file and return a list of company records"""
        # Check valid challenge_statement_id
        await BaseService(ChallengeStatement).get_by_id(challenge_statement_id, session)

        # Read file content
        content = await file.read()
        if not content:
            raise BadRequestException('CSV file is empty')
        df = None
        encodings = ['utf-8', 'utf-8-sig', 'latin1', 'iso-8859-1', 'cp1252']

        for encoding in encodings:
            try:
                df = pd.read_csv(
                    BytesIO(content),
                    engine='python',
                    encoding=encoding,
                    on_bad_lines='skip',  # Skip lines with encoding issues
                )
                break  # Successfully read the file
            except UnicodeDecodeError:
                continue  # Try next encoding
            except EmptyDataError as e:
                raise BadRequestException('CSV file is empty') from e

        if df is None:
            raise BadRequestException(
                'Could not read CSV file. Please ensure the file is properly encoded (UTF-8 preferred)'
            )

        # Check if DataFrame is empty or has only headers
        if df.empty:
            raise BadRequestException('CSV file contains only headers, no data')

        # Required columns
        missing_columns = [col for col in ['Name', 'WebsiteLink'] if col not in df.columns]
        if missing_columns:
            raise BadRequestException(f'Missing required columns: {", ".join(missing_columns)}')

        # Check for duplicate website links within the CSV
        duplicate_links = df[df['WebsiteLink'].duplicated()]['WebsiteLink'].unique()
        if len(duplicate_links) > 0:
            raise BadRequestException(f'Duplicate website links found in CSV: {", ".join(duplicate_links)}')

        # Get all website links from CSV
        website_links = df['WebsiteLink'].dropna().unique()

        # Check existing companies with these website links
        existing_companies_query = await session.execute(
            select(Company).where(Company.websiteLink.in_(website_links), Company.deletedAt.is_(None))
        )
        existing_companies = existing_companies_query.scalars().all()
        if existing_companies:
            duplicate_links = [str(company.websiteLink) for company in existing_companies]
            raise BadRequestException(
                f'Companies with following website links already exist: {", ".join(duplicate_links)}'
            )

        # Convert pandas NAN values to None
        df = df.replace({float('nan'): None})

        # Validate company data
        validation_errors = self.validate_company_data(df)
        if validation_errors:
            error_messages = []
            for column, errors in validation_errors.items():
                error_messages.extend([f'{column}: {error}' for error in errors])
            raise BadRequestException('\n'.join(error_messages))

        return await self.bulk_create_challenge_statement_company(
            challenge_statement_id, session, df, workspace_member_id
        )

    def validate_url(self, url: str | None) -> str | None:
        """Validate URL format"""
        if not url:
            return None
        try:
            result = urlparse(url)
            if all([result.scheme in ['http', 'https'], result.netloc]):
                return url
            raise ValueError('Invalid URL format')
        except Exception as e:
            raise ValueError(f'Invalid URL format: {str(e)}') from e

    def validate_company_data(  # pylint: disable=too-many-locals, too-many-branches, too-many-statements
        self, df: pd.DataFrame
    ) -> dict[str, list[str]]:
        """Validate company data from CSV

        Returns:
            Dictionary of validation errors by column
        """
        current_year = datetime.now(UTC).year
        errors: dict[str, list[str]] = {}

        # Validate Website URLs
        if 'WebsiteLink' in df.columns:
            invalid_urls = []
            for url in df['WebsiteLink'].dropna():
                try:
                    self.validate_url(url)
                except ValueError as e:
                    invalid_urls.append(f'{url}: {str(e)}')
            if invalid_urls:
                errors['WebsiteLink'] = invalid_urls

        # Validate LinkedIn URLs
        if 'CompanyLinkedInPage' in df.columns:
            invalid_urls = []
            for url in df['CompanyLinkedInPage'].dropna():
                try:
                    self.validate_url(url)
                    # if 'linkedin.com' not in url.lower():
                    #     invalid_urls.append(f'{url}: Not a valid LinkedIn URL')
                except ValueError as e:
                    invalid_urls.append(f'{url}: {str(e)}')
            if invalid_urls:
                errors['CompanyLinkedInPage'] = invalid_urls

        # Validate Year of Establishment
        if 'YearOfEstablishment' in df.columns:
            # Drop None/NA values before validation
            valid_years = df['YearOfEstablishment'].dropna()
            if not valid_years.empty:
                invalid_years = df[(df['YearOfEstablishment'] <= 0) | (df['YearOfEstablishment'] > current_year)][
                    'YearOfEstablishment'
                ].tolist()
                if invalid_years:
                    errors['YearOfEstablishment'] = [
                        f'Invalid years: {", ".join(map(str, invalid_years))}. Must be between 1 and {current_year}'
                    ]

        # Validate Number of Employees
        if 'NumberOfEmployees' in df.columns:
            valid_employees = df['NumberOfEmployees'].dropna()
            if not valid_employees.empty:
                invalid_employees = (
                    df[~df['NumberOfEmployees'].isin(CompanyNumberOfEmployeesMap.keys())]['NumberOfEmployees']
                    .unique()
                    .tolist()
                )
                if invalid_employees:
                    errors['NumberOfEmployees'] = [
                        f'Invalid employee ranges: {", ".join(map(str, invalid_employees))}. '
                        f'Must be one of: {", ".join(CompanyNumberOfEmployeesMap.keys())}'
                    ]

        # Validate Country of Incorporation
        if 'CountryOfIncorporation' in df.columns:
            valid_countries = df['CountryOfIncorporation'].dropna()
            if not valid_countries.empty:
                invalid_countries = (
                    df[~df['CountryOfIncorporation'].isin(Countries)]['CountryOfIncorporation'].unique().tolist()
                )
                if invalid_countries:
                    errors['CountryOfIncorporation'] = [
                        f'Invalid countries: {", ".join(map(str, invalid_countries))}. Must be a valid country code'
                    ]

        # Validate Country of Operation
        if 'CountryOfOperation' in df.columns:
            valid_rows = df['CountryOfOperation'].dropna()
            if not valid_rows.empty:
                invalid_countries_by_row = []

                for countries_str in valid_rows:
                    # Split by comma and strip whitespace
                    countries = [country.strip() for country in countries_str.split(',')]
                    # Find invalid countries
                    invalid_countries = [country for country in countries if country and country not in Countries]
                    if invalid_countries:
                        invalid_countries_by_row.extend(invalid_countries)

                if invalid_countries_by_row:
                    errors['CountryOfOperation'] = [
                        f'Invalid countries: {", ".join(map(str, set(invalid_countries_by_row)))}. '
                        'Must be a valid country code'
                    ]

        # Validate Company Stage
        if 'CompanyStage' in df.columns:
            valid_stages = df['CompanyStage'].dropna()
            if not valid_stages.empty:
                invalid_stages = df[~df['CompanyStage'].isin(CompanyStages)]['CompanyStage'].unique().tolist()
                if invalid_stages:
                    errors['CompanyStage'] = [
                        f'Invalid stages: {", ".join(map(str, invalid_stages))}. '
                        f'Must be one of: {", ".join(CompanyStages)}'
                    ]

        # Validate Industry Profile
        if 'IndustryProfile' in df.columns:
            valid_industries = df['IndustryProfile'].dropna()
            if not valid_industries.empty:
                invalid_industries = (
                    df[~df['IndustryProfile'].isin(CompanyIndustryProfiles)]['IndustryProfile'].unique().tolist()
                )
                if invalid_industries:
                    errors['IndustryProfile'] = [
                        f'Invalid industries: {", ".join(map(str, invalid_industries))}. '
                        f'Must be one of: {", ".join(CompanyIndustryProfiles)}'
                    ]

        return errors

    async def bulk_create_challenge_statement_company(  # pylint: disable=too-many-locals
        self, challenge_statement_id: UUID, session: SessionDep, df: pd.DataFrame, workspace_member_id: str
    ) -> str:
        """Create Company, ChallengeStatementCompany from dataframe"""
        # Create new companies
        new_companies = []
        try:
            # Find a workspace member with ID
            workspace_member_data = await add_actor_metadata({}, workspace_member_id, session)
            for _, row in df.iterrows():
                employees = 'A_ONE_TO_TEN'
                if row.get('NumberOfEmployees') and row['NumberOfEmployees'] in CompanyNumberOfEmployeesMap:
                    employees = CompanyNumberOfEmployeesMap[row['NumberOfEmployees']]
                incorporation_country = operation_country = stage = industry_profile = None
                if row.get('CountryOfIncorporation'):
                    incorporation_country = get_enum_value(row.get('CountryOfIncorporation'))
                if row.get('CountryOfOperation'):
                    operation_country = [
                        get_enum_value(operation_country.strip())
                        for operation_country in row.get('CountryOfOperation').split(',')
                    ]
                if row.get('CompanyStage'):
                    stage = get_enum_value(row.get('CompanyStage'))
                if row.get('IndustryProfile'):
                    industry_profile = get_enum_value(row.get('IndustryProfile'))
                # Get min position
                min_position_query = select(func.min(Company.position))
                result = await session.execute(min_position_query)
                min_position = result.scalar() or 0
                company = Company(
                    id=uuid4(),
                    name=row['Name'],
                    websiteLink=row['WebsiteLink'],
                    establishmentYear=row.get('YearOfEstablishment'),
                    linkedinLink=row.get('CompanyLinkedInPage'),
                    fundingHistory=row.get('FundingHistory'),
                    employees=employees,
                    incorporationCountry=incorporation_country,
                    operationCountry=operation_country,
                    stage=stage,
                    position=min_position - 1,
                    industryProfile=industry_profile,
                    **workspace_member_data,
                )
                session.add(company)

                # Additional Info and Sensitive Info
                session.add(
                    AdditionalInfo(
                        id=uuid4(),
                        companyId=company.id,
                    )
                )

                session.add(
                    SensitiveInfo(
                        id=uuid4(),
                        companyId=company.id,
                    )
                )
                new_companies.append(company)

            # Create multiple challenge statement company records
            challenge_statement_companies = []
            recommended_by_data = await add_actor_metadata({}, workspace_member_id, session, key='recommendedBy')
            for company in new_companies:
                challenge_statement_company = ChallengeStatementCompanies(
                    id=uuid4(),
                    companyId=company.id,
                    challengeStatementId=challenge_statement_id,
                    **recommended_by_data,
                    # Add other fields from CSV as needed
                )
                session.add(challenge_statement_company)
                challenge_statement_companies.append(challenge_statement_company)

            await session.commit()
            return 'Success'

        except Exception as exc:
            await session.rollback()
            raise DatabaseException('upload_csv', f'Failed to create companies: {str(exc)}') from exc

    async def move_challenge_statement_companies(  # pylint: disable=too-many-locals
        self,
        request: ChallengeStatementCompanyMoveRequest,
        session: SessionDep,
    ) -> str:
        """Create a challenge statement company record"""
        new_challenge_statement = await BaseService(ChallengeStatement).get_by_id(request.challengeStatementId, session)
        new_challenge_statement_companies_query = (
            select(ChallengeStatementCompanies)
            .join(ChallengeStatement, ChallengeStatementCompanies.challengeStatementId == ChallengeStatement.id)
            .where(
                ChallengeStatementCompanies.challengeStatementId == new_challenge_statement.id,
                ChallengeStatementCompanies.deletedAt.is_(None),
            )
        )
        result = await session.execute(new_challenge_statement_companies_query)
        new_challenge_statement_companies = result.scalars().all()
        linked_company_ids = {
            str(new_challenge_statement_company.companyId)
            for new_challenge_statement_company in new_challenge_statement_companies
        }
        challenge_statement_company_ids = request.companiesIds
        challenge_statement_companies_query = (
            select(ChallengeStatementCompanies)
            .join(ChallengeStatement, ChallengeStatementCompanies.challengeStatementId == ChallengeStatement.id)
            .where(
                ChallengeStatementCompanies.id.in_(request.companiesIds),
                ChallengeStatementCompanies.deletedAt.is_(None),
            )
            .options(
                selectinload(ChallengeStatementCompanies.challengeStatement),
                selectinload(ChallengeStatementCompanies.company),
            )
        )
        result = await session.execute(challenge_statement_companies_query)
        challenge_statement_companies = result.scalars().all()

        if (not challenge_statement_companies) or (
            len(challenge_statement_companies) != len(challenge_statement_company_ids)
        ):
            raise BadRequestException('Some challenge statement companies not found')

        # Validate all companies are from the same challenge statement
        unique_statement_ids = {company.challengeStatementId for company in challenge_statement_companies}
        request_company_ids = {
            str(challenge_statement_company.companyId) for challenge_statement_company in challenge_statement_companies
        }
        already_linked_company_ids = linked_company_ids & request_company_ids
        if len(unique_statement_ids) > 1:
            raise BadRequestException('All companies must be from the same challenge statement')

        # Validate challenge cycles match
        current_cycle_id = challenge_statement_companies[0].challengeStatement.challengeCycleId
        new_cycle_id = new_challenge_statement.challengeCycleId

        if current_cycle_id != new_cycle_id:
            raise BadRequestException(
                f'Cannot move companies between different challenge cycles. '
                f'Current cycle: {current_cycle_id}, New cycle: {new_cycle_id}'
            )

        try:
            # Update challengeStatementId
            update_statement_company_ids = [
                statement_company.id
                for statement_company in challenge_statement_companies
                if str(statement_company.companyId) not in already_linked_company_ids
            ]
            if update_statement_company_ids:
                await session.execute(
                    update(ChallengeStatementCompanies)
                    .where(ChallengeStatementCompanies.id.in_(update_statement_company_ids))
                    .values(challengeStatementId=request.challengeStatementId)
                )
                await session.commit()
        except Exception as exc:
            await session.rollback()
            raise DatabaseException(
                'move_challenge_statement_companies', f'Failed to move companies: {str(exc)}'
            ) from exc
        company_names = [
            str(challenge_statement_company.company.name)
            for challenge_statement_company in challenge_statement_companies
            if str(challenge_statement_company.companyId) not in already_linked_company_ids
        ]
        already_linked_company_names = [
            str(challenge_statement_company.company.name)
            for challenge_statement_company in challenge_statement_companies
            if str(challenge_statement_company.companyId) in already_linked_company_ids
        ]
        new_challenge_statement_key = f'CS-{str(int(new_challenge_statement.key)).zfill(2)}'
        extra_message = (
            f'Companies already linked with {new_challenge_statement_key}: {", ".join(already_linked_company_names)}.'
            if already_linked_company_names
            else ''
        )
        if len(company_names) > 1:
            return (
                f'{", ".join(company_names[:-1])} and {company_names[-1]} '
                f'have been moved to {new_challenge_statement_key}. {extra_message}'
            )
        if len(company_names) == 1:
            return f'{company_names[0]} has been moved to {new_challenge_statement_key}. {extra_message}'
        raise BadRequestException(extra_message)

    async def upload_solution_image(  # pylint: disable=too-many-locals
        self, challenge_statement_company_id: UUID, session: SessionDep, image: UploadFile
    ) -> str:
        """Upload solution image for a challenge statement company"""
        query = (
            select(
                ChallengeStatementCompanies,
                ChallengeStatement.key.label('statement_key'),
                ChallengeCycle.name.label('cycle_name'),
                Company.name.label('company_name'),
            )
            .join(ChallengeStatement, ChallengeStatementCompanies.challengeStatementId == ChallengeStatement.id)
            .join(ChallengeCycle, ChallengeStatement.challengeCycleId == ChallengeCycle.id)
            .join(Company, ChallengeStatementCompanies.companyId == Company.id)
            .where(
                ChallengeStatementCompanies.id == challenge_statement_company_id,
                ChallengeStatementCompanies.deletedAt.is_(None),
            )
        )
        result = await session.execute(query)
        row = result.first()  # Get the full row instead of just first column
        if row is None:
            raise NotFoundException(ChallengeStatementCompanies.__name__, str(challenge_statement_company_id))
        try:
            # Unpack the row values
            record, statement_key, cycle_name, company_name = row
            # Create file path
            formatted_statement_key = f'CS-{str(int(statement_key)).zfill(2)}'
            file_path = f'{cycle_name}/{formatted_statement_key}/{company_name}/{image.filename}'

            # Upload to blob storage
            image_content = await image.read()
            blob_url = await upload_file_to_blob(image_content, file_path)

            # Update record with URL
            record.solutionImage = blob_url
            await session.commit()

            return 'Image uploaded successfully'

        except Exception as exc:
            await session.rollback()
            raise DatabaseException('upload_solution_image', f'Failed to upload Solution Image: {str(exc)}') from exc


# Create a singleton instance
challenge_statement_company_service = ChallengeStatementCompanyService()
