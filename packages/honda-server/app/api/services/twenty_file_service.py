"""
Service to interact with Twenty server for Honda file URL generation
"""

import httpx
from typing import List, Dict, Optional
from app.core.config import settings


class TwentyFileService:
    """Service to generate signed URLs for Honda files via Twenty server"""
    
    def __init__(self):
        # Twenty server should be running on port 3000, Honda server on different port
        self.twenty_server_url = settings.FRONTEND_HOST  # Twenty server URL
        self.default_container = "honda-one-pager"  # Honda container name
        self.default_expires_in = "7d"
    
    async def generate_file_url(
        self,
        file_path: str,
        container_name: Optional[str] = None,
        expires_in: Optional[str] = None
    ) -> str:
        """
        Generate a signed URL for a single Honda file
        
        Args:
            file_path: Path to the file
            container_name: Azure container name
            expires_in: Token expiration time (defaults to "7d")
            
        Returns:
            Signed URL that can be used to access the file
        """

        request_data = {
            "filePath": file_path,
            "containerName": container_name or self.default_container,
            "expiresIn": expires_in or self.default_expires_in
        }


        try:
            async with httpx.AsyncClient() as client:
                response = await client.post(
                    f"{self.twenty_server_url}/honda-files/generate-url",
                    json=request_data,
                    timeout=30.0
                )

                if response.status_code == 201:
                    result = response.json()
                    return result["url"]
                else:
                    raise Exception(f"Failed to generate file URL: {response.status_code} - {response.text}")
        except Exception as e:
            raise Exception(f"Failed to generate file URL: {str(e)}")
    
    async def generate_multiple_file_urls(
        self,
        files: List[Dict[str, str]],
        container_name: Optional[str] = None,
        expires_in: Optional[str] = None
    ) -> List[Dict[str, str]]:
        """
        Generate signed URLs for multiple Honda files using batch endpoint

        Args:
            files: List of dicts with 'filePath' and optionally 'containerName'
            container_name: Default container name if not specified per file
            expires_in: Token expiration time (defaults to "7d")

        Returns:
            List of dicts with 'filePath' and 'url'
        """
        # Prepare files with container names
        files_with_containers = []
        for file_info in files:
            files_with_containers.append({
                "filePath": file_info["filePath"],
                "containerName": file_info.get("containerName", container_name or self.default_container)
            })

        request_data = {
            "files": files_with_containers,
            "expiresIn": expires_in or self.default_expires_in
        }

        try:
            async with httpx.AsyncClient() as client:
                response = await client.post(
                    f"{self.twenty_server_url}/honda-files/generate-multiple-urls",
                    json=request_data,
                    timeout=30.0
                )

                if response.status_code == 200:
                    result = response.json()
                    return result["urls"]
                else:
                    raise Exception(f"Failed to generate multiple file URLs: {response.status_code} - {response.text}")
        except Exception as e:
            raise Exception(f"Failed to generate multiple file URLs: {str(e)}")


# Global instance
twenty_file_service = TwentyFileService()
