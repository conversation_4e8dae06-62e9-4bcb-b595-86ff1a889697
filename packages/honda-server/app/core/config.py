"""
Define base class for settings, allowing values to be overridden by environment variables.
"""

from typing import Annotated, Any

from pydantic import AnyUrl, BeforeValidator, computed_field
from pydantic_settings import BaseSettings, SettingsConfigDict


def parse_cors(v: Any) -> list[str] | str:
    """
    Parse CORS from string or list value

    Args:
        v (Any): Input value to parse

    Raises:
        ValueError: Raised when v is not str or list

    Returns:
        list[str] | str: Value after parse
    """
    if isinstance(v, str) and not v.startswith('['):
        return [i.strip() for i in v.split(',')]
    if isinstance(v, list | str):
        return v
    raise ValueError(v)


class Settings(BaseSettings):
    """
    Base class for settings, allowing values to be overridden by environment variables.
    """

    model_config = SettingsConfigDict(
        env_file='.env',
        env_ignore_empty=True,
        extra='ignore',
    )
    FRONTEND_HOST: str = ''
    PROJECT_NAME: str = ''
    STATIC_ROUTE_EP: str | None = ''
    APP_SECRET: str = ''
    PG_DATABASE_URL: str = ''
    REDIS_URL: str = ''
    STORAGE_AZURE_CONNECTION_STRING: str = ''
    STORAGE_AZURE_CONTAINER_NAME: str = ''
    BACKEND_CORS_ORIGINS: Annotated[list[AnyUrl] | str, BeforeValidator(parse_cors)] = []


    @computed_field  # type: ignore[prop-decorator]
    @property
    def all_cors_origins(self) -> list[str]:
        """
        Get All Cors Origin

        Returns:
            list[str]: list of cors origins
        """
        return [str(origin).rstrip('/') for origin in self.BACKEND_CORS_ORIGINS] + [self.FRONTEND_HOST]


settings = Settings()
