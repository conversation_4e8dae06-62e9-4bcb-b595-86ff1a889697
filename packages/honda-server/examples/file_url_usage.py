"""
Example usage of the Honda file URL generation system
"""

import asyncio
from services.twenty_file_service import twenty_file_service


async def example_single_file_url():
    """Example: Generate a signed URL for a single PDF file"""
    
    file_path = "challenge-statements/company-123/onepager.pdf"
    
    try:
        signed_url = await twenty_file_service.generate_file_url(
            file_path=file_path,
            container_name="honda-pdfs",
            expires_in="7d"  # 7 days expiration
        )
        
        print(f"Generated signed URL: {signed_url}")
        return signed_url
        
    except Exception as e:
        print(f"Error generating URL: {e}")
        return None


async def example_multiple_file_urls():
    """Example: Generate signed URLs for multiple files"""
    
    files = [
        {"filePath": "challenge-statements/company-123/onepager.pdf"},
        {"filePath": "challenge-statements/company-456/onepager.pdf"},
        {"filePath": "solution-images/company-789/solution.jpg", "containerName": "honda-images"},
    ]
    
    try:
        signed_urls = await twenty_file_service.generate_multiple_file_urls(
            files=files,
            container_name="honda-pdfs",  # Default container
            expires_in="7d"
        )
        
        print("Generated signed URLs:")
        for result in signed_urls:
            if result.get('url'):
                print(f"  {result['filePath']} -> {result['url']}")
            else:
                print(f"  {result['filePath']} -> ERROR: {result.get('error', 'Unknown error')}")
                
        return signed_urls
        
    except Exception as e:
        print(f"Error generating URLs: {e}")
        return []


async def example_challenge_statement_companies_with_urls():
    """Example: How to use the enhanced API endpoint"""
    
    import httpx
    
    # Call your Honda server API with signed URLs enabled
    async with httpx.AsyncClient() as client:
        response = await client.get(
            "http://localhost:8000/challenge-statement-companies/",
            params={
                "challengeStatementId": "123e4567-e89b-12d3-a456-426614174000",
                "include_signed_urls": True,
                "page": 1,
                "page_size": 10
            }
        )
        
        if response.status_code == 200:
            data = response.json()
            
            print("Challenge Statement Companies with signed URLs:")
            for company in data.get('data', []):
                print(f"Company: {company.get('name', 'Unknown')}")
                print(f"  Original URL: {company.get('onePagerUrl', 'None')}")
                print(f"  Signed URL: {company.get('onePagerSignedUrl', 'None')}")
                print()
                
        else:
            print(f"API call failed: {response.status_code} - {response.text}")


async def example_direct_url_generation():
    """Example: Use the dedicated URL generation endpoint"""
    
    import httpx
    
    file_paths = [
        "challenge-statements/company-123/onepager.pdf",
        "challenge-statements/company-456/onepager.pdf",
        "solution-images/company-789/solution.jpg"
    ]
    
    async with httpx.AsyncClient() as client:
        response = await client.post(
            "http://localhost:8000/challenge-statement-company/generate-signed-urls",
            json={
                "file_paths": file_paths,
                "container_name": "honda-pdfs",
                "expires_in": "7d"
            }
        )
        
        if response.status_code == 200:
            data = response.json()
            print("Generated signed URLs:")
            for result in data.get('signed_urls', []):
                print(f"  {result['filePath']} -> {result.get('url', 'ERROR')}")
        else:
            print(f"URL generation failed: {response.status_code} - {response.text}")


async def example_form_generation_with_30day_urls():
    """Example: Get companies for form generation with 30-day URLs"""

    import httpx

    challenge_statement_id = "123e4567-e89b-12d3-a456-426614174000"

    async with httpx.AsyncClient() as client:
        response = await client.post(
            "http://localhost:8000/challenge-statement-company/form-generation",
            json={
                "challenge_statement_id": challenge_statement_id,
                "company_ids": [  # Optional: specific companies
                    "456e7890-e89b-12d3-a456-426614174001",
                    "789e0123-e89b-12d3-a456-426614174002"
                ]
            }
        )

        if response.status_code == 200:
            data = response.json()
            print("Companies for form generation (30-day URLs):")
            for company in data.get('companies', []):
                print(f"Company: {company.get('name', 'Unknown')}")
                print(f"  Original onePagerURL: {company.get('onePagerURL', 'None')}")
                print(f"  30-day Signed URL: {company.get('onePagerURLSignedUrl', 'None')}")
                print(f"  Solution Image: {company.get('solutionImageSignedUrl', 'None')}")
                print()
        else:
            print(f"Form generation failed: {response.status_code} - {response.text}")


async def example_list_with_signed_urls():
    """Example: List companies with signed URLs (7-day default)"""

    import httpx

    async with httpx.AsyncClient() as client:
        response = await client.get(
            "http://localhost:8000/challenge-statement-companies/",
            params={
                "challengeStatementId": "123e4567-e89b-12d3-a456-426614174000",
                "include_signed_urls": True,  # This triggers signed URL generation
                "page": 1,
                "page_size": 10
            }
        )

        if response.status_code == 200:
            data = response.json()
            print("Challenge Statement Companies with 7-day signed URLs:")
            for company in data.get('items', []):
                print(f"Company ID: {company.get('id', 'Unknown')}")
                print(f"  Original onePagerURL: {company.get('onePagerURL', 'None')}")
                print(f"  7-day Signed URL: {company.get('onePagerURLSignedUrl', 'None')}")
                print(f"  Solution Image Signed URL: {company.get('solutionImageSignedUrl', 'None')}")
                print()
        else:
            print(f"API call failed: {response.status_code} - {response.text}")


if __name__ == "__main__":
    print("=== Honda File URL Generation Examples ===\n")

    print("1. Single file URL generation:")
    asyncio.run(example_single_file_url())
    print()

    print("2. Multiple file URL generation:")
    asyncio.run(example_multiple_file_urls())
    print()

    print("3. List companies with 7-day signed URLs:")
    asyncio.run(example_list_with_signed_urls())
    print()

    print("4. Form generation with 30-day URLs:")
    asyncio.run(example_form_generation_with_30day_urls())
    print()

    print("5. Direct URL generation endpoint:")
    asyncio.run(example_direct_url_generation())
