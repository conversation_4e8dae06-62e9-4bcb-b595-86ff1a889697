"""
Test script to verify batch URL generation functionality
"""

import asyncio
import httpx


async def test_batch_url_generation():
    """Test the batch URL generation endpoint"""
    
    # Test data
    files = [
        {"filePath": "fds/CS-01/Company1.pdf", "containerName": "honda-one-pager"},
        {"filePath": "fds/CS-01/Company2.pdf", "containerName": "honda-one-pager"},
        {"filePath": "fds/CS-02/Company3.pdf", "containerName": "honda-one-pager"},
    ]
    
    request_data = {
        "files": files,
        "expiresIn": "7d"
    }
    
    print("Testing batch URL generation...")
    print(f"Request data: {request_data}")
    
    try:
        async with httpx.AsyncClient() as client:
            # Test direct call to Twenty server batch endpoint
            response = await client.post(
                "http://localhost:3000/honda-files/generate-multiple-urls",
                json=request_data,
                timeout=30.0
            )
            
            print(f"Response status: {response.status_code}")
            
            if response.status_code == 200:
                result = response.json()
                print("✅ Batch URL generation successful!")
                print("Generated URLs:")
                for url_info in result["urls"]:
                    print(f"  {url_info['filePath']} -> {url_info['url'][:100]}...")
            else:
                print(f"❌ Batch URL generation failed: {response.status_code} - {response.text}")
                
    except Exception as e:
        print(f"❌ Error testing batch URL generation: {e}")


async def test_honda_service_batch():
    """Test the Honda service batch functionality"""
    
    # Import the service
    import sys
    sys.path.append('/home/<USER>/Desktop/dev/honda/deploy/crm-service/packages/honda-server')
    
    from app.api.services.twenty_file_service import twenty_file_service
    
    files = [
        {"filePath": "fds/CS-01/Company1.pdf"},
        {"filePath": "fds/CS-01/Company2.pdf"},
        {"filePath": "fds/CS-02/Company3.pdf"},
    ]
    
    print("\nTesting Honda service batch functionality...")
    
    try:
        signed_urls = await twenty_file_service.generate_multiple_file_urls(
            files=files,
            container_name="honda-one-pager",
            expires_in="7d"
        )
        
        print("✅ Honda service batch generation successful!")
        print("Generated URLs:")
        for result in signed_urls:
            if result.get('url'):
                print(f"  {result['filePath']} -> {result['url'][:100]}...")
            else:
                print(f"  {result['filePath']} -> ERROR: {result.get('error', 'Unknown error')}")
                
    except Exception as e:
        print(f"❌ Error testing Honda service batch: {e}")


async def main():
    print("=== Testing Batch URL Generation ===\n")
    
    print("1. Testing direct Twenty server batch endpoint:")
    await test_batch_url_generation()
    
    print("\n2. Testing Honda service batch functionality:")
    await test_honda_service_batch()


if __name__ == "__main__":
    asyncio.run(main())
