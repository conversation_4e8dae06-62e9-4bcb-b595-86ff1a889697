"""
Test script to verify form generation URL signing functionality
"""

import asyncio
import httpx
import json


async def test_form_generation_url_signing():
    """Test the complete form generation URL signing flow"""
    
    # Simulate the data that would come from selectedCompanies
    test_companies = [
        {
            "id": "company-1",
            "name": "Test Company 1",
            "onePagerURL": "fds/CS-01/Company1.pdf"
        },
        {
            "id": "company-2", 
            "name": "Test Company 2",
            "onePagerURL": "fds/CS-01/Company2.pdf"
        },
        {
            "id": "company-3",
            "name": "Test Company 3", 
            "onePagerURL": "fds/CS-02/Company3.pdf"
        }
    ]
    
    # Extract file paths (same logic as frontend)
    file_paths = [
        company["onePagerURL"] 
        for company in test_companies 
        if company["onePagerURL"] and company["onePagerURL"].strip() != ""
    ]
    
    print("=== Testing Form Generation URL Signing ===\n")
    print(f"Test companies: {len(test_companies)}")
    print(f"File paths to sign: {file_paths}")
    
    # Test the generate-signed-urls endpoint
    request_data = {
        "file_paths": file_paths,
        "container_name": "honda-one-pager",
        "expires_in": "30d"  # 30-day expiration for forms
    }
    
    print(f"\nRequest data: {json.dumps(request_data, indent=2)}")
    
    try:
        async with httpx.AsyncClient() as client:
            # Test Honda server endpoint
            response = await client.post(
                "http://localhost:8000/api/challenge-statement-companies/generate-signed-urls",
                json=request_data,
                timeout=30.0
            )
            
            print(f"\nResponse status: {response.status_code}")
            
            if response.status_code == 200:
                result = response.json()
                signed_urls = result["signed_urls"]
                
                print("✅ URL signing successful!")
                print(f"Generated {len(signed_urls)} signed URLs:")
                
                # Create URL map (same logic as frontend)
                url_map = {}
                for item in signed_urls:
                    if item.get('url'):
                        url_map[item['filePath']] = item['url']
                
                print(f"\nURL Map: {json.dumps(url_map, indent=2)}")
                
                # Map signed URLs back to companies (same logic as frontend)
                companies_with_signed_urls = []
                for company in test_companies:
                    updated_company = company.copy()
                    if company["onePagerURL"] and company["onePagerURL"] in url_map:
                        updated_company["onePagerURL"] = url_map[company["onePagerURL"]]
                        print(f"✅ Mapped {company['name']}: {company['onePagerURL']} -> {updated_company['onePagerURL'][:100]}...")
                    else:
                        print(f"⚠️  No signed URL for {company['name']}: {company['onePagerURL']}")
                    companies_with_signed_urls.append(updated_company)
                
                print(f"\n✅ Form generation data ready with {len(companies_with_signed_urls)} companies")
                
                # Test one of the signed URLs
                if signed_urls and signed_urls[0].get('url'):
                    test_url = signed_urls[0]['url']
                    print(f"\n🔗 Testing signed URL access: {test_url[:100]}...")
                    
                    try:
                        test_response = await client.get(test_url, timeout=10.0)
                        if test_response.status_code == 200:
                            print("✅ Signed URL is accessible!")
                        else:
                            print(f"❌ Signed URL returned: {test_response.status_code}")
                    except Exception as e:
                        print(f"❌ Error accessing signed URL: {e}")
                
            else:
                print(f"❌ URL signing failed: {response.status_code} - {response.text}")
                
    except Exception as e:
        print(f"❌ Error testing form generation URL signing: {e}")


async def test_direct_twenty_server():
    """Test direct call to Twenty server batch endpoint"""
    
    print("\n=== Testing Direct Twenty Server Batch Endpoint ===\n")
    
    files = [
        {"filePath": "fds/CS-01/Company1.pdf", "containerName": "honda-one-pager"},
        {"filePath": "fds/CS-01/Company2.pdf", "containerName": "honda-one-pager"},
        {"filePath": "fds/CS-02/Company3.pdf", "containerName": "honda-one-pager"},
    ]
    
    request_data = {
        "files": files,
        "expiresIn": "30d"
    }
    
    print(f"Request data: {json.dumps(request_data, indent=2)}")
    
    try:
        async with httpx.AsyncClient() as client:
            response = await client.post(
                "http://localhost:3000/honda-files/generate-multiple-urls",
                json=request_data,
                timeout=30.0
            )
            
            print(f"Response status: {response.status_code}")
            
            if response.status_code == 200:
                result = response.json()
                print("✅ Direct Twenty server call successful!")
                print(f"Generated URLs: {json.dumps(result, indent=2)}")
            else:
                print(f"❌ Direct Twenty server call failed: {response.status_code} - {response.text}")
                
    except Exception as e:
        print(f"❌ Error testing direct Twenty server: {e}")


async def main():
    print("=== Form Generation URL Signing Test Suite ===\n")
    
    await test_form_generation_url_signing()
    await test_direct_twenty_server()


if __name__ == "__main__":
    asyncio.run(main())
