"""Azure Blob Storage utilities"""

from azure.storage.blob import BlobServiceClient

from app.core.config import settings


async def upload_file_to_blob(
    file_content: bytes,
    file_name: str,
) -> str:
    """Upload a file to Azure Blob Storage and return URL"""

    # Create blob client
    blob_service_client = BlobServiceClient.from_connection_string(settings.STORAGE_AZURE_CONNECTION_STRING)
    container_client = blob_service_client.get_container_client(settings.STORAGE_AZURE_CONTAINER_NAME)

    # Upload file
    blob_client = container_client.get_blob_client(file_name)
    blob_client.upload_blob(file_content, overwrite=True)

    print('----------------', blob_client)

    return blob_client.blob_name
