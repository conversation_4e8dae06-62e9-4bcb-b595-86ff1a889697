import useChallengeCycle from '@/api/hooks/useChallengeCycle';
import useEvaluationSubmission from '@/api/hooks/useEvaluationSubmission';
import { CoreObjectNameSingular } from '@/object-metadata/types/CoreObjectNameSingular';
import { AppPath } from '@/types/AppPath';
import { CampaignsPath } from '@/types/CampaignsPath';
import { SnackBarVariant } from '@/ui/feedback/snack-bar-manager/components/SnackBar';
import { useSnackBar } from '@/ui/feedback/snack-bar-manager/hooks/useSnackBar';
import { Select } from '@/ui/input/components/Select';
import { TextInput } from '@/ui/input/components/TextInput';
import { SubMenuTopBarContainer } from '@/ui/layout/page/components/SubMenuTopBarContainer';
import { useTheme } from '@emotion/react';
import styled from '@emotion/styled';
import { t } from '@lingui/core/macro';
import { Trans } from '@lingui/react/macro';
import { useEffect, useState } from 'react';
import { useNavigate, useParams } from 'react-router-dom';
import {
  IconLibrary,
  IconLink,
  IconMenu2,
  IconSearch,
  isDefined,
  RawLink,
  Section,
  Tag,
  TagColor,
  UndecoratedLink,
} from 'twenty-ui';
import { useDebouncedCallback } from 'use-debounce';
import { CampaignsBackButton } from '~/pages/campaigns/components/CampaignsBackButton';
import { CampaignsPageContainer } from '~/pages/campaigns/components/CampaignsPageContainer';
import { CampaignsTable } from '~/pages/campaigns/components/table/CampaignsTable';
import { CampaignsTablePagination } from '~/pages/campaigns/components/table/CampaignsTablePagination';
import { CampaignsTableTagCell } from '~/pages/campaigns/components/table/cells/CampaignsTableTagCell';
import { ChallengeCycle } from '~/pages/campaigns/components/types/ChallengeCycle';
import { ChallengeStatement } from '~/pages/campaigns/components/types/ChallengeStatement';
import {
  COMPANY_GRADING,
  COMPANY_GRADING_OPTIONS,
  mostVotedCompanyGrading,
  NEXT_ACTION_STAGE_2_OPTIONS,
} from '~/pages/campaigns/components/types/CompanyGradingType';
import { EVALUATION_RESULTS_STAGE } from '~/pages/campaigns/evaluation-results/types/EvaluationResultsType';
import { MoveCompanyModalWrapper } from '~/pages/campaigns/submissions/components/MoveCompanyModalWrapper';
import { revertEnumValue } from '~/pages/campaigns/utils/getEnumValue';
import {
  ACTION_CODE,
  PERMISSION_CODE,
  useHasPermission,
} from '~/pages/settings/hooks/useHasPermission';

import { formatStatementKey } from '~/utils/format/formatStatementKey';
import { getAppPath } from '~/utils/navigation/getAppPath';
import { getCampaignsPath } from '~/utils/navigation/getCampaignsPath';

const StyledTitle = styled.div`
  align-items: center;
  color: ${({ theme }) => theme.font.color.primary};
  display: flex;
  font-size: ${({ theme }) => theme.font.size.xxl};
  font-weight: ${({ theme }) => theme.font.weight.semiBold};
  margin: ${({ theme }) => theme.spacing(3)} 0;
  gap: ${({ theme }) => theme.spacing(2)};
  width: fit-content;
`;

const StyledTableText = styled.div`
  margin-bottom: ${({ theme }) => theme.spacing(3)};
  margin-top: ${({ theme }) => theme.spacing(4)};
`;

const StyledSearchInput = styled(TextInput)`
  padding-bottom: ${({ theme }) => theme.spacing(2)};
  width: 100%;
`;

// eslint-disable-next-line @nx/workspace-no-hardcoded-colors
const StyledLinkText = styled.div`
  color: #295eff;
  text-decoration: underline;
`;

const StyledTableContainer = styled.div`
  align-items: center;
  display: flex;
  flex-direction: column;
  height: 100%;
  justify-content: space-between;
`;

const StyledAddButtonWrapper = styled.div`
  position: fixed;
  bottom: 30px;
  background-color: ${({ theme }) => theme.background.primary};
  margin-top: ${({ theme }) => theme.spacing(6)};
  align-items: center;
  display: flex;
  gap: ${({ theme }) => theme.spacing(2)};
  justify-content: center;
  border: 1px solid ${({ theme }) => theme.color.gray30};
  border-radius: ${({ theme }) => theme.border.radius.sm};
  padding: ${({ theme }) => theme.spacing(2)} ${({ theme }) => theme.spacing(4)};
  width: fit-content;
`;

const StyledAddButton = styled.div`
  border: 1px solid ${({ theme }) => theme.color.gray30};
  align-items: center;
  border-radius: ${({ theme }) => theme.border.radius.sm};
  color: ${({ theme }) => theme.font.color.primary};
  cursor: pointer;
  display: flex;
  font-size: ${({ theme }) => theme.font.size.md};
  font-weight: ${({ theme }) => theme.font.weight.semiBold};
  gap: ${({ theme }) => theme.spacing(1)};
  padding: ${({ theme }) => theme.spacing(1)} ${({ theme }) => theme.spacing(2)};
  text-align: center;
`;

type EvaluationStage1CompanySelectionProps = {
  challengeStatement: ChallengeStatement | null;
};

export const EvaluationStage1CompanySelection = ({
  challengeStatement,
}: EvaluationStage1CompanySelectionProps) => {
  const { getChallengeCycle, getChallengeStatementCompanies } =
    useChallengeCycle();

  const {
    getListMultipleSubmissions,
    getListEvaluationResultStage1,
    createEvaluationResult,
    updateEvaluationResult,
  } = useEvaluationSubmission();
  const pathParams = useParams<{ id: string }>();
  const { enqueueSnackBar } = useSnackBar();
  const theme = useTheme();
  const navigate = useNavigate();

  const [challengeCycle, setChallengeCycle] = useState<ChallengeCycle | null>(
    null,
  );
  const [hasCompanyData, setHasCompanyData] = useState<boolean>(false);
  const [tableData, setTableData] = useState<any[]>([]);
  const [selectedRows, setSelectedRows] = useState<Record<number, boolean>>({});
  const [selectAllStatus, setSelectAllStatus] = useState<
    'all' | 'some' | 'none'
  >('none');
  const [isMoveCompanyModalOpen, setIsMoveCompanyModalOpen] =
    useState<boolean>(false);

  const [getCompanies, setGetCompanies] = useState<boolean>(false);

  const [searchTerm, setSearchTerm] = useState<string>('');
  const [searchFilter, setSearchFilter] = useState<string>('');
  const [currentPage, setCurrentPage] = useState(1);
  const [itemsPerPage, setItemsPerPage] = useState(10);
  const [totalRecord, setTotalRecord] = useState<number>(0);

  const debouncedSetFilter = useDebouncedCallback((value) => {
    setCurrentPage(1);
    setSearchFilter(value);
  }, 500);

  const hasEditPermission = useHasPermission(
    PERMISSION_CODE.CHALLENGE_CYCLE_ENTITY,
    ACTION_CODE.EDIT,
  );

  const challengeStatementName =
    challengeStatement?.key && challengeStatement?.statement
      ? formatStatementKey(challengeStatement.key) +
        ': ' +
        challengeStatement.statement
      : '';

  const columns = [
    {
      name: 'Company',
      width: 120,
      selector: (row: any) => (
        <UndecoratedLink
          to={getCampaignsPath(CampaignsPath.SubmissionStatementCompany, {
            id: row.id ? row.id : '',
          })}
          fullWidth
        >
          <CampaignsTableTagCell value={row.company.name} />
        </UndecoratedLink>
      ),
    },
    {
      name: 'Country of Incorporation',
      width: 160,
      selector: (row: any) =>
        row.company.incorporationCountry
          ? revertEnumValue(row.company.incorporationCountry)
          : '',
    },
    {
      name: 'Recommended by',
      width: 120,
      selector: (row: any) => (
        <CampaignsTableTagCell value={row.recommendedByName} />
      ),
    },
    {
      name: 'Key Offering',
      width: 80,
      selector: (row: any) =>
        row.onePagerURLSigned && (
          <RawLink href={row.onePagerURLSigned || ''}>
            <StyledLinkText>Link here</StyledLinkText>
          </RawLink>
        ),
    },
    {
      name: 'Next Action',
      width: 150,
      noPadding: hasEditPermission,
      selector: (row: any) =>
        hasEditPermission ? (
          <Select
            dropdownId={`drop-down-id-next-action-${row.id}`}
            dropdownWidthAuto
            value={row.nextAction}
            emptyOption={{ label: 'Select', value: '' }}
            options={Object.entries(NEXT_ACTION_STAGE_2_OPTIONS).map(
              ([key, value]) => ({
                label: value.label,
                value: key,
                color: value.color,
              }),
            )}
            onChange={(value) =>
              setNextActionValue(
                value,
                row.id,
                row.evaluationResultId,
                row.mostVoted,
              )
            }
            fullWidth
            isTableSelect={true}
            type="tag"
          />
        ) : (
          NEXT_ACTION_STAGE_2_OPTIONS[row.nextAction]?.label && (
            <Tag
              color={
                NEXT_ACTION_STAGE_2_OPTIONS[row.nextAction]?.color as TagColor
              }
              text={NEXT_ACTION_STAGE_2_OPTIONS[row.nextAction]?.label}
            />
          )
        ),
    },
    {
      name: 'Most Voted Results',
      width: 240,
      noPadding: true,
      selector: (row: any) => (
        <CampaignsTableTagCell
          value={COMPANY_GRADING_OPTIONS[row.mostVoted]?.label}
          color={COMPANY_GRADING_OPTIONS[row.mostVoted]?.color as TagColor}
          withMargin
        />
      ),
    },
    {
      name: 'Final Results (Stage 1)',
      width: 240,
      noPadding: hasEditPermission,
      selector: (row: any) =>
        hasEditPermission ? (
          <Select
            dropdownId={`drop-down-id-result-${row.id}`}
            dropdownWidthAuto
            value={row.final ?? COMPANY_GRADING.TO_BE_GRADED}
            emptyOption={{ label: 'Select', value: '' }}
            options={Object.entries(COMPANY_GRADING_OPTIONS).map(
              ([key, value]) => ({
                label: value.label,
                value: key,
                color: value.color,
              }),
            )}
            onChange={(value) =>
              setFinalResultValue(
                value,
                row.id,
                row.evaluationResultId,
                row.mostVoted,
              )
            }
            fullWidth
            isTableSelect={true}
            type="tag"
          />
        ) : (
          <Tag
            color={COMPANY_GRADING_OPTIONS[row.final]?.color as TagColor}
            text={
              COMPANY_GRADING_OPTIONS[row.final ?? COMPANY_GRADING.TO_BE_GRADED]
                ?.label
            }
          />
        ),
    },
  ];

  const [extraColumns, setExtraColumns] = useState<any[]>([]);

  const finalColumns = columns.concat(extraColumns);

  useEffect(() => {
    if (!hasCompanyData) return;
    const getChallengeCycleData = async (id: string) => {
      const res = await getChallengeCycle(id);
      if (isDefined(res)) {
        setChallengeCycle(res);
        setExtraColumns(
          res.evaluatorInfos.map((item: any) => ({
            name: `${item.nameFirstName} ${item.nameLastName}'s Grading`,
            width: 240,
            noPadding: true,
            selector: (row: any) => (
              <CampaignsTableTagCell
                value={COMPANY_GRADING_OPTIONS[row[item.id]]?.label}
                color={COMPANY_GRADING_OPTIONS[row[item.id]]?.color as TagColor}
                withMargin
              />
            ),
          })),
        );
      }
    };
    const getSubmissionData = async (id: string) => {
      const res = await getListMultipleSubmissions({
        challengeStatementId: id,
      });
      if (isDefined(res.data.submissions)) {
        setTableData((prev) => {
          return prev.map((company) => {
            const currentSubmission = res.data.submissions.filter(
              (item: any) => item.challengeStatementCompanyId === company.id,
            );
            if (currentSubmission.length > 0) {
              const currentData = company;
              currentSubmission.forEach((item: any) => {
                currentData[item.internalEvaluatorId] =
                  item.evaluation?._internal?.grade ?? '';
              });
              currentData.mostVoted = mostVotedCompanyGrading(
                currentSubmission.map(
                  (item: any) => item.evaluation?._internal?.grade,
                ),
              );
              return currentData;
            }
            return company;
          });
        });
      }
    };
    const getResultData = async (id: string) => {
      const res = await getListEvaluationResultStage1({
        challengeStatementId: id,
      });
      if (isDefined(res.data.result)) {
        const resultMap = new Map<string, any>(
          res.data.result.map((item: any) => [
            item.challengeStatementCompanyId,
            item,
          ]),
        );
        setTableData((prev) => {
          return prev.map((company) => {
            const currentResult = resultMap.get(company.id);
            if (isDefined(currentResult)) {
              return {
                ...company,
                mostVoted: currentResult.result?.mostVoted ?? '',
                final: currentResult.result?.final ?? '',
                nextAction: currentResult.nextAction,
                evaluationResultId: currentResult.id,
              };
            }
            return company;
          });
        });
      }
    };
    if (isDefined(challengeStatement?.challengeCycleId)) {
      getChallengeCycleData(challengeStatement.challengeCycleId);
    }
    if (isDefined(challengeStatement?.id)) {
      getSubmissionData(challengeStatement.id);
      getResultData(challengeStatement.id);
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [challengeStatement, hasCompanyData]);

  useEffect(() => {
    const getChallengeStatementCompanyData = async (id: string) => {
      const res = await getChallengeStatementCompanies({
        challengeStatementId: id,
        filter: JSON.stringify({ companyName: searchFilter }),
        sort: JSON.stringify([['updatedAt', 'desc']]),
        page: currentPage,
        page_size: itemsPerPage,
      });
      if (isDefined(res.data)) {
        setTableData(res.data.items);
        setTotalRecord(res.data.total);
        setHasCompanyData(true);
      }
    };
    if (isDefined(pathParams.id)) {
      getChallengeStatementCompanyData(pathParams.id);
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [getCompanies, currentPage, itemsPerPage, searchFilter]);

  const setFinalResultValue = async (
    value: string,
    id: string,
    evaluationResultId: string,
    mostVoted: string,
  ) => {
    try {
      if (isDefined(evaluationResultId)) {
        await updateEvaluationResult(evaluationResultId, {
          finalResult: value,
        });
      } else {
        const createRes = await createEvaluationResult({
          challengeStatementCompanyId: id,
          challengeStatementId: challengeStatement?.id,
          stageId: challengeCycle?.evaluationStages[0].id,
          result: {
            mostVoted: mostVoted ?? '',
            final: value,
          },
          nextAction: null,
        });
        if (isDefined(createRes)) {
          setSelectValue(createRes.data.id, id, 'evaluationResultId');
        }
      }
      setSelectValue(value, id, 'final');
    } catch (err) {
      enqueueSnackBar(
        `${isDefined(evaluationResultId) ? 'Update' : 'Create'} evaluation submissions failed.`,
        { variant: SnackBarVariant.Error },
      );
    }
  };

  const setNextActionValue = async (
    value: string,
    id: string,
    evaluationResultId: string,
    mostVoted: string,
  ) => {
    try {
      if (isDefined(evaluationResultId)) {
        await updateEvaluationResult(evaluationResultId, {
          nextAction: value,
        });
      } else {
        const createRes = await createEvaluationResult({
          challengeStatementCompanyId: id,
          challengeStatementId: challengeStatement?.id,
          stageId: challengeCycle?.evaluationStages[0].id,
          result: {
            mostVoted: mostVoted ?? '',
            final: '',
          },
          nextAction: value,
        });
        if (isDefined(createRes)) {
          setSelectValue(createRes.data.id, id, 'evaluationResultId');
        }
      }
      setSelectValue(value, id, 'nextAction');
    } catch (err) {
      enqueueSnackBar(
        `${isDefined(evaluationResultId) ? 'Update' : 'Create'} evaluation submissions failed.`,
        { variant: SnackBarVariant.Error },
      );
    }
  };

  const setSelectValue = (value: string, id: string, type: string) => {
    setTableData((prev) =>
      prev.map((item: any) =>
        item.id === id ? { ...item, [type]: value } : item,
      ),
    );
  };

  const goToFormGeneration = async () => {
    const selectedCompanies = tableData
      .filter((item: any) => selectedRows[item.id])
      .map((item: any) => ({
        id: item.id,
        name: item.company?.name,
        onePagerURL: item.onePagerURL,
      }));

    // Extract file paths for signed URL generation
    const filePaths = selectedCompanies
      .map((company: any) => company.onePagerURL)
      .filter((url: string) => url && url.trim() !== ''); // Filter out empty URLs

    let companiesWithSignedUrls = selectedCompanies;

    // Generate signed URLs with 30-day expiration for form generation
    if (filePaths.length > 0) {
      try {
        const response = await fetch(
          '/api/challenge-statement-companies/generate-signed-urls',
          {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
            },
            body: JSON.stringify({
              file_paths: filePaths,
              container_name: 'honda-one-pager', // Honda container
              expires_in: '30d', // 30-day expiration for forms
            }),
          },
        );

        if (response.ok) {
          const { signed_urls } = await response.json();

          // Create a map of filePath -> signed URL for efficient lookup
          const urlMap = new Map();
          signed_urls.forEach((item: any) => {
            if (isDefined(item.url)) {
              urlMap.set(item.filePath, item.url);
            }
          });

          // Map signed URLs back to companies
          companiesWithSignedUrls = selectedCompanies.map((company: any) => ({
            ...company,
            onePagerURL:
              company.onePagerURL && urlMap.has(company.onePagerURL)
                ? urlMap.get(company.onePagerURL)
                : company.onePagerURL, // Use signed URL if available, otherwise keep original
          }));
        } else {
          // Continue with original URLs if signing fails
        }
      } catch (error) {
        // Continue with original URLs if signing fails
      }
    }

    const params = {
      challengeStatementName: challengeStatementName,
      challengeStatementId: challengeStatement?.id,
      challengeCycleCriteria: challengeCycle?.evaluationStages.find(
        (stage: any) => stage.stage === 2,
      )?.criteria,
      selectedCompanies: companiesWithSignedUrls,
    };

    navigate(getCampaignsPath(CampaignsPath.EvaluationResultFormCreate), {
      state: params,
    });
  };

  return (
    <SubMenuTopBarContainer
      links={[
        {
          children: <Trans>Campaigns</Trans>,
        },
        {
          children: challengeStatement?.challengeCycleName ?? '',
          href: getCampaignsPath(CampaignsPath.ChallengeCycleDetail, {
            id: challengeStatement?.challengeCycleId ?? '',
          }),
        },
        {
          children: <Trans>Evaluation Results</Trans>,
          href: getCampaignsPath(CampaignsPath.EvaluationResultDetail, {
            id: challengeStatement?.challengeCycleId ?? '',
          }),
        },
        {
          children: 'Stage 1 Results',
          href: getCampaignsPath(CampaignsPath.EvaluationResultDetail, {
            id: challengeStatement?.challengeCycleId ?? '',
          }),
        },
        {
          children: challengeStatement?.key
            ? formatStatementKey(challengeStatement?.key)
            : '',
        },
      ]}
    >
      <CampaignsPageContainer>
        <Section>
          <CampaignsBackButton
            to={getCampaignsPath(
              CampaignsPath.EvaluationResultDetail,
              {
                id: challengeStatement?.challengeCycleId ?? '',
              },
              { stage: EVALUATION_RESULTS_STAGE.STAGE_1 },
            )}
          />
          <UndecoratedLink
            to={getAppPath(AppPath.RecordShowPage, {
              objectNameSingular: CoreObjectNameSingular.ChallengeStatement,
              objectRecordId: challengeStatement?.id ?? '',
            })}
          >
            <StyledTitle>
              {challengeStatementName}
              <IconLink />
            </StyledTitle>
          </UndecoratedLink>
          <StyledTableText>{challengeStatement?.detail}</StyledTableText>
          <StyledTableText>
            Select the companies that made it to the next stage to generate an
            evaluation form.
          </StyledTableText>
          <StyledSearchInput
            LeftIcon={IconSearch}
            placeholder={t`Search`}
            value={searchTerm}
            onChange={(value: string) => {
              setSearchTerm(value);
              debouncedSetFilter(value);
            }}
          />

          <StyledTableContainer>
            <CampaignsTable
              columns={finalColumns}
              data={tableData}
              selectedRows={selectedRows}
              setSelectedRows={setSelectedRows}
              selectAllStatus={selectAllStatus}
              setSelectAllStatus={setSelectAllStatus}
            />
            <CampaignsTablePagination
              currentPage={currentPage}
              itemsPerPage={itemsPerPage}
              totalItems={totalRecord}
              onPageChange={setCurrentPage}
              onItemsPerPageChange={setItemsPerPage}
            />
            {Object.keys(selectedRows).length > 0 && (
              <StyledAddButtonWrapper>
                {Object.keys(selectedRows).length} selected:
                <StyledAddButton onClick={goToFormGeneration}>
                  <IconLibrary size={theme.icon.size.md} />
                  Generate form
                </StyledAddButton>
                <StyledAddButton
                  onClick={() => setIsMoveCompanyModalOpen(true)}
                >
                  <IconMenu2 size={theme.icon.size.md} />
                  Move company
                </StyledAddButton>
              </StyledAddButtonWrapper>
            )}
            {isMoveCompanyModalOpen && (
              <MoveCompanyModalWrapper
                onClose={() => setIsMoveCompanyModalOpen(false)}
                currentStatement={challengeStatement}
                selectedRows={selectedRows}
                setSelectedRows={setSelectedRows}
                setSelectAllStatus={setSelectAllStatus}
                setGetCompanies={setGetCompanies}
              />
            )}
          </StyledTableContainer>
        </Section>
      </CampaignsPageContainer>
    </SubMenuTopBarContainer>
  );
};
