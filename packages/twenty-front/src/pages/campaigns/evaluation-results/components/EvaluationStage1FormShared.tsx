import useChallengeCycle from '@/api/hooks/useChallengeCycle';
import useEvaluationSubmission from '@/api/hooks/useEvaluationSubmission';
import { CoreObjectNameSingular } from '@/object-metadata/types/CoreObjectNameSingular';
import { AppPath } from '@/types/AppPath';
import { CampaignsPath } from '@/types/CampaignsPath';
import { SnackBarVariant } from '@/ui/feedback/snack-bar-manager/components/SnackBar';
import { useSnackBar } from '@/ui/feedback/snack-bar-manager/hooks/useSnackBar';
import { SubMenuTopBarContainer } from '@/ui/layout/page/components/SubMenuTopBarContainer';
import styled from '@emotion/styled';
import { Trans } from '@lingui/react/macro';
import { useEffect, useState } from 'react';
import { useParams } from 'react-router-dom';
import {
  Button,
  H2Title,
  IconLink,
  isDefined,
  RawLink,
  Section,
  Tag,
  TagColor,
  UndecoratedLink,
} from 'twenty-ui';
import { VUE_APP_BASE_URL } from '~/config';
import { CampaignsBackButton } from '~/pages/campaigns/components/CampaignsBackButton';
import { CampaignsPageContainer } from '~/pages/campaigns/components/CampaignsPageContainer';
import { CampaignsTable } from '~/pages/campaigns/components/table/CampaignsTable';
import { CampaignsTableTagCell } from '~/pages/campaigns/components/table/cells/CampaignsTableTagCell';
import { ChallengeStatement } from '~/pages/campaigns/components/types/ChallengeStatement';
import {
  COMPANY_GRADING_OPTIONS,
  mostVotedCompanyGrading,
  NEXT_ACTION_STAGE_2_OPTIONS,
} from '~/pages/campaigns/components/types/CompanyGradingType';
import { EVALUATION_RESULTS_STAGE } from '~/pages/campaigns/evaluation-results/types/EvaluationResultsType';
import { revertEnumValue } from '~/pages/campaigns/utils/getEnumValue';

import { formatStatementKey } from '~/utils/format/formatStatementKey';
import { getAppPath } from '~/utils/navigation/getAppPath';
import { getCampaignsPath } from '~/utils/navigation/getCampaignsPath';

const StyledTitle = styled.div`
  align-items: center;
  color: ${({ theme }) => theme.font.color.primary};
  display: flex;
  font-size: ${({ theme }) => theme.font.size.xxl};
  font-weight: ${({ theme }) => theme.font.weight.semiBold};
  margin: ${({ theme }) => theme.spacing(3)} 0;
  gap: ${({ theme }) => theme.spacing(2)};
  width: fit-content;
`;

const StyledTableText = styled.div`
  margin-bottom: ${({ theme }) => theme.spacing(3)};
  margin-top: ${({ theme }) => theme.spacing(4)};
`;

// eslint-disable-next-line @nx/workspace-no-hardcoded-colors
const StyledLinkText = styled.div`
  color: #295eff;
  text-decoration: underline;
`;

const StyledH2Title = styled(H2Title)`
  margin-top: ${({ theme }) => theme.spacing(4)};
`;

const StyledButtonWrapper = styled.div`
  display: flex;
  gap: ${({ theme }) => theme.spacing(4)};
`;

const StyledTag = styled(Tag)`
  min-width: fit-content;
`;

type EvaluationStage1FormSharedProps = {
  challengeStatement: ChallengeStatement | null;
};

export const EvaluationStage1FormShared = ({
  challengeStatement,
}: EvaluationStage1FormSharedProps) => {
  const { getChallengeCycle, getChallengeStatementCompanies } =
    useChallengeCycle();

  const { getListMultipleSubmissions, getListEvaluationResultStage1 } =
    useEvaluationSubmission();
  const pathParams = useParams<{ id: string }>();

  const [hasCompanyData, setHasCompanyData] = useState<boolean>(false);
  const [tableData, setTableData] = useState<any[]>([]);

  const { enqueueSnackBar } = useSnackBar();

  const formUrl = `${VUE_APP_BASE_URL}/forms/${challengeStatement?.formURL}`;

  const challengeStatementName =
    challengeStatement?.key && challengeStatement?.statement
      ? formatStatementKey(challengeStatement.key) +
        ': ' +
        challengeStatement.statement
      : '';

  const columns = [
    {
      name: 'Company',
      width: 120,
      selector: (row: any) => (
        <UndecoratedLink
          to={getCampaignsPath(CampaignsPath.SubmissionStatementCompany, {
            id: row.id ? row.id : '',
          })}
          fullWidth
        >
          <CampaignsTableTagCell value={row.company.name} />
        </UndecoratedLink>
      ),
    },
    {
      name: 'Country of Incorporation',
      width: 160,
      selector: (row: any) =>
        row.company.incorporationCountry
          ? revertEnumValue(row.company.incorporationCountry)
          : '',
    },
    {
      name: 'Recommended by',
      width: 120,
      selector: (row: any) => (
        <CampaignsTableTagCell value={row.recommendedByName} />
      ),
    },
    {
      name: 'Key Offering',
      width: 80,
      selector: (row: any) =>
        row.onePagerURLSigned && (
          <RawLink href={row.onePagerURLSigned || ''}>
            <StyledLinkText>Link here</StyledLinkText>
          </RawLink>
        ),
    },
    {
      name: 'Next Action',
      width: 150,
      noPadding: true,
      selector: (row: any) => (
        <CampaignsTableTagCell
          value={NEXT_ACTION_STAGE_2_OPTIONS[row.nextAction]?.label}
          color={NEXT_ACTION_STAGE_2_OPTIONS[row.nextAction]?.color as TagColor}
          withMargin
        />
      ),
    },
    {
      name: 'Most Voted Results',
      width: 240,
      noPadding: true,
      selector: (row: any) => (
        <CampaignsTableTagCell
          value={COMPANY_GRADING_OPTIONS[row.mostVoted]?.label}
          color={COMPANY_GRADING_OPTIONS[row.mostVoted]?.color as TagColor}
          withMargin
        />
      ),
    },
    {
      name: 'Final Results (Stage 1)',
      width: 240,
      noPadding: true,
      selector: (row: any) => (
        <CampaignsTableTagCell
          value={COMPANY_GRADING_OPTIONS[row.final]?.label}
          color={COMPANY_GRADING_OPTIONS[row.final]?.color as TagColor}
          withMargin
        />
      ),
    },
  ];

  const [extraColumns, setExtraColumns] = useState<any[]>([]);

  const finalCoumns = columns.concat(extraColumns);

  useEffect(() => {
    if (!hasCompanyData) return;
    const getChallengeCycleData = async (id: string) => {
      const res = await getChallengeCycle(id);
      if (isDefined(res)) {
        setExtraColumns(
          res.evaluatorInfos.map((item: any) => ({
            name: `${item.nameFirstName} ${item.nameLastName}'s Grading`,
            width: 240,
            noPadding: true,
            selector: (row: any) => (
              <CampaignsTableTagCell
                value={COMPANY_GRADING_OPTIONS[row[item.id]]?.label}
                color={COMPANY_GRADING_OPTIONS[row[item.id]]?.color as TagColor}
                withMargin
              />
            ),
          })),
        );
      }
    };
    const getSubmissionData = async (id: string) => {
      const res = await getListMultipleSubmissions({
        challengeStatementId: id,
      });
      if (isDefined(res.data.submissions)) {
        setTableData((prev) => {
          return prev.map((company) => {
            const currentSubmission = res.data.submissions.filter(
              (item: any) => item.challengeStatementCompanyId === company.id,
            );
            if (currentSubmission.length > 0) {
              const currentData = company;
              currentSubmission.forEach((item: any) => {
                currentData[item.internalEvaluatorId] =
                  item.evaluation?._internal?.grade ?? '';
              });
              currentData.mostVoted = mostVotedCompanyGrading(
                currentSubmission.map(
                  (item: any) => item.evaluation?._internal?.grade,
                ),
              );
              return currentData;
            }
            return company;
          });
        });
      }
    };
    const getResultData = async (id: string) => {
      const res = await getListEvaluationResultStage1({
        challengeStatementId: id,
      });
      if (isDefined(res.data.result)) {
        const resultMap = new Map<string, any>(
          res.data.result.map((item: any) => [
            item.challengeStatementCompanyId,
            item,
          ]),
        );
        setTableData((prev) => {
          return prev.map((company) => {
            const currentResult = resultMap.get(company.id);
            if (isDefined(currentResult)) {
              return {
                ...company,
                final: currentResult.result?.final ?? '',
                nextAction: currentResult.nextAction,
                evaluationResultId: currentResult.id,
              };
            }
            return company;
          });
        });
      }
    };
    if (isDefined(challengeStatement?.challengeCycleId)) {
      getChallengeCycleData(challengeStatement.challengeCycleId);
    }
    if (isDefined(challengeStatement?.id)) {
      getSubmissionData(challengeStatement.id);
      getResultData(challengeStatement.id);
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [challengeStatement, hasCompanyData]);

  useEffect(() => {
    const getChallengeStatementCompanyData = async (id: string) => {
      const res = await getChallengeStatementCompanies({
        challengeStatementId: id,
      });
      if (isDefined(res.data)) {
        setTableData(res.data.items);
        setHasCompanyData(true);
      }
    };
    if (isDefined(pathParams.id)) {
      getChallengeStatementCompanyData(pathParams.id);
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  const handleCopyLink = () => {
    const textarea = document.createElement('textarea');
    textarea.value = formUrl;
    textarea.setAttribute('readonly', '');
    textarea.style.position = 'absolute';
    textarea.style.left = '-9999px';
    document.body.appendChild(textarea);
    textarea.select();
    try {
      document.execCommand('copy');
      enqueueSnackBar('Link copied to clipboard', {
        variant: SnackBarVariant.Success,
        duration: 2000,
      });
    } catch (err) {
      enqueueSnackBar('Failed to copy!', {
        variant: SnackBarVariant.Error,
        duration: 2000,
      });
    }
  };

  return (
    <SubMenuTopBarContainer
      links={[
        {
          children: <Trans>Campaigns</Trans>,
        },
        {
          children: challengeStatement?.challengeCycleName ?? '',
          href: getCampaignsPath(CampaignsPath.ChallengeCycleDetail, {
            id: challengeStatement?.challengeCycleId ?? '',
          }),
        },
        {
          children: <Trans>Evaluation Results</Trans>,
          href: getCampaignsPath(CampaignsPath.EvaluationResultDetail, {
            id: challengeStatement?.challengeCycleId ?? '',
          }),
        },
        {
          children: 'Stage 1 Results',
          href: getCampaignsPath(CampaignsPath.EvaluationResultDetail, {
            id: challengeStatement?.challengeCycleId ?? '',
          }),
        },
        {
          children: challengeStatement?.key
            ? formatStatementKey(challengeStatement?.key)
            : '',
        },
      ]}
    >
      <CampaignsPageContainer>
        <Section>
          <CampaignsBackButton
            to={getCampaignsPath(
              CampaignsPath.EvaluationResultDetail,
              {
                id: challengeStatement?.challengeCycleId ?? '',
              },
              { stage: EVALUATION_RESULTS_STAGE.STAGE_1 },
            )}
          />
          <UndecoratedLink
            to={getAppPath(AppPath.RecordShowPage, {
              objectNameSingular: CoreObjectNameSingular.ChallengeStatement,
              objectRecordId: challengeStatement?.id ?? '',
            })}
          >
            <StyledTitle>
              <StyledTag color="purple" text="Form shared" />
              {challengeStatementName}
              <IconLink />
            </StyledTitle>
          </UndecoratedLink>
          <StyledTableText>{challengeStatement?.detail}</StyledTableText>
          <StyledButtonWrapper>
            <Button
              title="Preview form"
              size="medium"
              onClick={() => {
                window.open(formUrl, '_blank');
              }}
            />
            <Button
              title="Copy form link"
              size="medium"
              onClick={handleCopyLink}
            />
          </StyledButtonWrapper>

          <StyledH2Title title="Selected companies for Stage 2" />
          <CampaignsTable
            columns={finalCoumns}
            data={tableData.filter((item: any) =>
              challengeStatement?.selected_company_ids?.includes(item.id),
            )}
          />

          <StyledH2Title title="Unselected companies" />
          <CampaignsTable
            columns={finalCoumns}
            data={tableData.filter(
              (item: any) =>
                !challengeStatement?.selected_company_ids?.includes(item.id),
            )}
          />
        </Section>
      </CampaignsPageContainer>
    </SubMenuTopBarContainer>
  );
};
