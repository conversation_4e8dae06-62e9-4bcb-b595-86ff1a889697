import useChallengeCycle from '@/api/hooks/useChallengeCycle';
import useEvaluationSubmission from '@/api/hooks/useEvaluationSubmission';
import { CoreObjectNameSingular } from '@/object-metadata/types/CoreObjectNameSingular';
import { AppPath } from '@/types/AppPath';
import { CampaignsPath } from '@/types/CampaignsPath';
import { SnackBarVariant } from '@/ui/feedback/snack-bar-manager/components/SnackBar';
import { useSnackBar } from '@/ui/feedback/snack-bar-manager/hooks/useSnackBar';
import { Select } from '@/ui/input/components/Select';
import { TextInput } from '@/ui/input/components/TextInput';
import { SubMenuTopBarContainer } from '@/ui/layout/page/components/SubMenuTopBarContainer';
import styled from '@emotion/styled';
import { t } from '@lingui/core/macro';
import { Trans } from '@lingui/react/macro';
import { useEffect, useState } from 'react';
import { CSVLink } from 'react-csv';
import { useParams } from 'react-router-dom';
import {
  Button,
  IconLink,
  IconMessage,
  IconSearch,
  isDefined,
  RawLink,
  Section,
  Tag,
  TagColor,
  UndecoratedLink,
} from 'twenty-ui';
import { VUE_APP_BASE_URL } from '~/config';
import { CHALLENGE_CYCLE_CRITERIA_SCORING_TYPE } from '~/pages/campaigns/challenge-cycles/types/NewChallengeCycleType';
import { CampaignsBackButton } from '~/pages/campaigns/components/CampaignsBackButton';
import { CampaignsPageContainer } from '~/pages/campaigns/components/CampaignsPageContainer';
import { CampaignsTable } from '~/pages/campaigns/components/table/CampaignsTable';
import { CampaignsTableTagCell } from '~/pages/campaigns/components/table/cells/CampaignsTableTagCell';
import { CampaignsTableFilter } from '~/pages/campaigns/components/table/filter/CampaignsTableFilter';
import { ChallengeCycle } from '~/pages/campaigns/components/types/ChallengeCycle';
import { ChallengeStatement } from '~/pages/campaigns/components/types/ChallengeStatement';
import {
  NEXT_ACTION_STAGE_2_OPTIONS,
  NEXT_ACTION_STAGE_3,
  NEXT_ACTION_STAGE_3_OPTIONS,
} from '~/pages/campaigns/components/types/CompanyGradingType';
import { CommentModalWrapper } from '~/pages/campaigns/evaluation-results/components/CommentModalWrapper';
import { CriteriaCommentModalWrapper } from '~/pages/campaigns/evaluation-results/components/CriteriaCommentModalWrapper';
import { EVALUATION_RESULTS_STAGE } from '~/pages/campaigns/evaluation-results/types/EvaluationResultsType';
import { accessNestedProperty } from '~/pages/campaigns/utils/accessNestedProperty';
import { revertEnumValue } from '~/pages/campaigns/utils/getEnumValue';
import { valueToString } from '~/pages/campaigns/utils/valueToString';
import {
  ACTION_CODE,
  PERMISSION_CODE,
  useHasPermission,
} from '~/pages/settings/hooks/useHasPermission';

import { formatStatementKey } from '~/utils/format/formatStatementKey';
import { getAppPath } from '~/utils/navigation/getAppPath';
import { getCampaignsPath } from '~/utils/navigation/getCampaignsPath';

const StyledTitle = styled.div`
  align-items: center;
  color: ${({ theme }) => theme.font.color.primary};
  display: flex;
  font-size: ${({ theme }) => theme.font.size.xxl};
  font-weight: ${({ theme }) => theme.font.weight.semiBold};
  margin: ${({ theme }) => theme.spacing(3)} 0;
  gap: ${({ theme }) => theme.spacing(2)};
  width: fit-content;
`;

const StyledTableText = styled.div`
  margin-bottom: ${({ theme }) => theme.spacing(3)};
  margin-top: ${({ theme }) => theme.spacing(4)};
`;

const StyledSearchInput = styled(TextInput)`
  width: 100%;
`;

// eslint-disable-next-line @nx/workspace-no-hardcoded-colors
const StyledLinkText = styled.div`
  color: #295eff;
  text-decoration: underline;
`;

const StyledTableContainer = styled.div`
  align-items: center;
  display: flex;
  flex-direction: column;
  height: 100%;
  justify-content: space-between;
`;

const StyledButtonWrapper = styled.div`
  display: flex;
  gap: ${({ theme }) => theme.spacing(4)};
  margin-bottom: ${({ theme }) => theme.spacing(4)};
`;

const StyledEvaluatorSelect = styled.div`
  margin: ${({ theme }) => theme.spacing(4)} 0;
  width: 240px;
`;

const StyledActionWrapper = styled.div`
  align-items: center;
  display: flex;
  justify-content: space-between;
  margin: ${({ theme }) => theme.spacing(4)} 0;
`;

const StyledFilterWrapper = styled.div`
  margin-left: ${({ theme }) => theme.spacing(4)};
  width: 300px;
`;

const StyledCSVLink = styled(CSVLink)`
  text-decoration: none;
`;

export const useEvaluationStageCompanyTable = ({
  currentStage,
}: {
  currentStage: string;
}) => {
  const {
    getChallengeStatement,
    getChallengeCycle,
    getChallengeStatementCompanies,
    getChallengeStatementEvaluator,
  } = useChallengeCycle();

  const {
    getListMultipleSubmissions,
    getListEvaluationResultStage1,
    updateEvaluationResult,
    getListEvaluationComment,
  } = useEvaluationSubmission();
  const pathParams = useParams<{ id: string }>();
  const { enqueueSnackBar } = useSnackBar();

  const [challengeCycle, setChallengeCycle] = useState<ChallengeCycle | null>(
    null,
  );
  const [challengeStatement, setChallengeStatement] =
    useState<ChallengeStatement | null>(null);

  const [hasCompanyData, setHasCompanyData] = useState<boolean>(false);
  const [tableData, setTableData] = useState<any[]>([]);
  const [selectedRows, setSelectedRows] = useState<Record<number, boolean>>({});
  const [selectAllStatus, setSelectAllStatus] = useState<
    'all' | 'some' | 'none'
  >('none');

  const [isCommentModalOpen, setIsCommentModalOpen] = useState<boolean>(false);
  const [isCriteriaCommentModalOpen, setIsCriteriaCommentModalOpen] =
    useState<boolean>(false);
  const [selectedCommentRow, setSelectedCommentRow] = useState<any>(null);

  const [selectedEvaluator, setSelectedEvaluator] = useState<string>('');
  const [listEvaluatorOptions, setListEvaluatorOptions] = useState<any[]>([
    { label: 'Aggregated scores', value: '' },
  ]);

  const [searchTerm, setSearchTerm] = useState<string>('');
  const [filteredTableData, setFilteredTableData] = useState<any[]>([]);

  const [exportAdditionalData, setExportAdditionalData] = useState<any>({
    comments: [],
  });
  const [refetchAdditionalComment, setRefetchAdditionalComment] =
    useState<boolean>(false);

  const [tableLoading, setTableLoading] = useState<boolean>(true);
  const [triggerTableLoading, setTriggerTableLoading] =
    useState<boolean>(false);

  const hasEditPermission = useHasPermission(
    PERMISSION_CODE.CHALLENGE_CYCLE_ENTITY,
    ACTION_CODE.EDIT,
  );

  const challengeStatementName =
    challengeStatement?.key && challengeStatement?.statement
      ? formatStatementKey(challengeStatement.key) +
        ': ' +
        challengeStatement.statement
      : '';

  const hiddenColumnListForCSV = [
    'Country of Incorporation',
    'Recommended by',
    'Key Offering',
    'Next Action',
    'Preferred',
    'Comments',
  ];

  const columns = [
    {
      name: 'Company',
      sortable: true,
      fieldValue: 'company.name',
      width: 200,
      selector: (row: any) => (
        <UndecoratedLink
          to={getCampaignsPath(CampaignsPath.SubmissionStatementCompany, {
            id: row.id ? row.id : '',
          })}
          fullWidth
        >
          <CampaignsTableTagCell value={row.company.name} />
        </UndecoratedLink>
      ),
    },
    {
      name: 'Country of Incorporation',
      sortable: true,
      fieldValue: 'company.incorporationCountry',
      width: 160,
      selector: (row: any) =>
        row.company.incorporationCountry
          ? revertEnumValue(row.company.incorporationCountry)
          : '',
    },
    {
      name: 'Recommended by',
      sortable: true,
      fieldValue: 'recommendedByName',
      width: 130,
      selector: (row: any) => (
        <CampaignsTableTagCell value={row.recommendedByName} />
      ),
    },
    {
      name: 'Key Offering',
      sortable: true,
      fieldValue: 'onePagerURLSigned',
      width: 90,
      selector: (row: any) =>
        row.onePagerURLSigned && (
          <RawLink href={row.onePagerURLSigned || ''}>
            <StyledLinkText>Link here</StyledLinkText>
          </RawLink>
        ),
    },
    {
      name: 'Results',
      sortable: true,
      fieldValue: 'results',
      width: 50,
      selector: (row: any) => row.results,
    },
    {
      name: 'Next Action',
      hidden: selectedEvaluator !== '',
      sortable: true,
      fieldValue: 'nextAction',
      width: 150,
      noPadding: hasEditPermission,
      selector: (row: any) =>
        hasEditPermission ? (
          <Select
            dropdownId={`drop-down-id-next-action-${row.id}`}
            dropdownWidthAuto
            value={row.nextAction}
            emptyOption={{ label: 'Select', value: '' }}
            options={Object.entries(
              currentStage === EVALUATION_RESULTS_STAGE.STAGE_2
                ? NEXT_ACTION_STAGE_2_OPTIONS
                : NEXT_ACTION_STAGE_3_OPTIONS,
            ).map(([key, value]) => ({
              label: value.label,
              value: key,
              color: value.color,
              disabled: value.disabled,
            }))}
            onChange={(value) =>
              setNextActionValue(value, row.id, row.evaluationResultId)
            }
            fullWidth
            isTableSelect={true}
            type="tag"
          />
        ) : currentStage === EVALUATION_RESULTS_STAGE.STAGE_2 ? (
          <Tag
            color={
              NEXT_ACTION_STAGE_2_OPTIONS[row.nextAction]?.color as TagColor
            }
            text={NEXT_ACTION_STAGE_2_OPTIONS[row.nextAction]?.label}
          />
        ) : (
          <Tag
            color={
              NEXT_ACTION_STAGE_3_OPTIONS[row.nextAction]?.color as TagColor
            }
            text={NEXT_ACTION_STAGE_3_OPTIONS[row.nextAction]?.label}
          />
        ),
    },
    {
      name: 'Preferred',
      hidden: selectedEvaluator === '',
      sortable: true,
      fieldValue: 'preferred',
      width: 60,
      selector: (row: any) => row.preferred,
    },
    {
      name: 'Comments',
      hidden: currentStage === EVALUATION_RESULTS_STAGE.STAGE_3,
      width: 80,
      selector: (row: any) => (
        <div onClick={() => openCommentsModal(row)}>
          <IconMessage color="gray" size="16" />
        </div>
      ),
    },
  ];

  const [extraColumns, setExtraColumns] = useState<any[]>([]);
  const [selectedField, setSelectedField] = useState<any>(columns[0]);

  columns.splice(5, 0, ...extraColumns);

  useEffect(() => {
    const getChallengeStatementData = async (id: string) => {
      const res = await getChallengeStatement(id);
      if (isDefined(res)) {
        setChallengeStatement(res);
      }
    };
    if (isDefined(pathParams.id)) {
      getChallengeStatementData(pathParams.id);
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  useEffect(() => {
    if (triggerTableLoading) {
      setTableLoading(false);
      setTriggerTableLoading(false);
    }
  }, [tableData, setTriggerTableLoading, triggerTableLoading]);

  useEffect(() => {
    if (!hasCompanyData) return;
    const getChallengeCycleData = async (id: string) => {
      const res = await getChallengeCycle(id);
      if (isDefined(res)) {
        setChallengeCycle(res);
        if (isDefined(res.evaluationStages[1]?.criteria)) {
          setExtraColumns(
            Object.values(res.evaluationStages[1]?.criteria).map(
              (item: any, index: number) => {
                const typeValue =
                  item.type ===
                  CHALLENGE_CYCLE_CRITERIA_SCORING_TYPE.RANGE_ONE_TO_FIVE
                    ? '(/5)'
                    : '';
                const nameValue = `Criteria ${index + 1} ${item.name} ${typeValue}`;
                if (
                  item.type ===
                  CHALLENGE_CYCLE_CRITERIA_SCORING_TYPE.EVALUATOR_COMMENT
                ) {
                  return {
                    name: nameValue,
                    fieldValue: `criteriaGrading[${index}]`,
                    width: 200,
                    selector: (row: any) => (
                      <div
                        onClick={() =>
                          openCriteriaCommentsModal({
                            name: nameValue,
                            data: row.criteriaGrading?.[index],
                            companyName: row.company.name,
                          })
                        }
                      >
                        <IconMessage color="gray" size="16" />
                      </div>
                    ),
                  };
                }
                return {
                  name: nameValue,
                  sortable: true,
                  fieldValue: `criteriaGrading[${index}]`,
                  width: 200,
                  selector: (row: any) => row.criteriaGrading?.[index],
                };
              },
            ),
          );
        }
      }
    };
    if (isDefined(challengeStatement?.challengeCycleId)) {
      getChallengeCycleData(challengeStatement.challengeCycleId);
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [challengeStatement, hasCompanyData]);

  useEffect(() => {
    if (!hasCompanyData || !challengeCycle) return;
    const getResultData = async (id: string) => {
      const res = await getListEvaluationResultStage1({
        challengeStatementId: id,
        stageId: challengeCycle?.evaluationStages[1]?.id,
        nextAction:
          currentStage === EVALUATION_RESULTS_STAGE.STAGE_3
            ? [
                NEXT_ACTION_STAGE_3.PROCEED_TO_STAGE_3,
                NEXT_ACTION_STAGE_3.WINNER,
                NEXT_ACTION_STAGE_3.DROP,
              ]
            : [],
      });
      if (isDefined(res.data.result)) {
        setTableData((prev) => {
          return prev.reduce((acc: any[], company: any) => {
            const currentSubmission = res.data.result.filter(
              (item: any) => item?.challengeStatementCompanyId === company.id,
            );

            if (currentSubmission.length === 0) {
              return acc;
            }

            const currentData = { ...company };

            currentSubmission.forEach((item: any) => {
              currentData.criteriaGrading = [
                valueToString(item.result?.c1?.answer),
                valueToString(item.result?.c2?.answer),
                valueToString(item.result?.c3?.answer),
                valueToString(item.result?.c4?.answer),
              ];
              currentData.results = item.result?.total;
              currentData.nextAction = item.nextAction;
              currentData.evaluationResultId = item.id;
              currentData.submission = item ?? '';
              currentData.comments = [[], [], []];
              item.result.comment.forEach((comment: any) => {
                const currentIndex =
                  comment?.name === 'Follow-up questions (if any)'
                    ? 2
                    : comment?.name === 'What concerned me'
                      ? 1
                      : 0;
                currentData.comments[currentIndex] = [
                  ...currentData.comments[currentIndex],
                  `${comment.externalEvaluatorName}: ${comment.answer ?? ''}`,
                ];
              });
              currentData.comments = currentData.comments.map(
                (commentsArray: any) => commentsArray.join(', '),
              );
            });

            acc.push(currentData);
            return acc;
          }, []);
        });

        setTriggerTableLoading(true);
      }
    };
    const getSubmissionData = async (id: string) => {
      const res = await getListMultipleSubmissions({
        challengeStatementId: id,
        externalEvaluatorEmail:
          selectedEvaluator !== '' ? selectedEvaluator : null,
      });
      if (isDefined(res.data.submissions)) {
        setTableData((prev) => {
          return prev.map((company) => {
            const currentSubmission = res.data.submissions.filter(
              (item: any) => item?.challengeStatementCompanyId === company.id,
            );
            if (currentSubmission.length > 0) {
              const currentData = company;
              currentSubmission.forEach((item: any) => {
                currentData.criteriaGrading = [
                  valueToString(item.evaluation?.c1?.answer),
                  valueToString(item.evaluation?.c2?.answer),
                  valueToString(item.evaluation?.c3?.answer),
                  valueToString(item.evaluation?.c4?.answer),
                ];
                currentData.results = item.evaluation.total;
                currentData.submission = item ?? '';
                currentData.preferred = item?.preferred ? 'Yes' : 'No';
              });
              return currentData;
            }
            return company;
          });
        });
      }
    };

    if (isDefined(challengeStatement?.id)) {
      if (selectedEvaluator !== '') {
        getSubmissionData(challengeStatement.id);
      } else {
        getResultData(challengeStatement.id);
      }
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [challengeCycle, challengeStatement, hasCompanyData, selectedEvaluator]);

  useEffect(() => {
    const getListAdditionalComment = async () => {
      const listId = tableData.map((item: any) => item.id);
      if (listId.length > 0) {
        const res = await getListEvaluationComment({
          challengeStatementCompanyIds: listId,
        });
        if (isDefined(res.data)) {
          setExportAdditionalData(res.data);
        }
      }
    };
    if (isDefined(challengeStatement?.id)) {
      getListAdditionalComment();
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [challengeStatement, refetchAdditionalComment, tableData]);

  useEffect(() => {
    const getChallengeStatementCompanyData = async (id: string) => {
      const res = await getChallengeStatementCompanies({
        challengeStatementId: id,
      });
      if (isDefined(res.data)) {
        setTableData(res.data.items);
        setHasCompanyData(true);
      }
    };
    const getChallengeStatementEvaluatorData = async (id: string) => {
      const res = await getChallengeStatementEvaluator(id);
      if (isDefined(res)) {
        setListEvaluatorOptions([
          { label: 'Aggregated scores', value: '' },
          ...res.map((evaluator: any) => ({
            label: evaluator.name,
            value: evaluator.email,
          })),
        ]);
      }
    };
    if (isDefined(pathParams.id)) {
      getChallengeStatementCompanyData(pathParams.id);
      getChallengeStatementEvaluatorData(pathParams.id);
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  const setNextActionValue = async (
    value: string,
    id: string,
    evaluationResultId: string,
  ) => {
    try {
      if (isDefined(evaluationResultId)) {
        await updateEvaluationResult(evaluationResultId, {
          nextAction: value,
        });
      }
      setSelectValue(value, id, 'nextAction');
    } catch (err) {
      enqueueSnackBar(
        `${isDefined(evaluationResultId) ? 'Update' : 'Create'} evaluation submissions failed.`,
        { variant: SnackBarVariant.Error },
      );
    }
  };

  const setSelectValue = (value: string, id: string, type: string) => {
    setTableData((prev) =>
      prev.map((item: any) =>
        item.id === id ? { ...item, [type]: value } : item,
      ),
    );
  };

  const openCommentsModal = (row: any) => {
    setSelectedCommentRow(row);
    setIsCommentModalOpen(true);
  };

  const openCriteriaCommentsModal = (row: any) => {
    setSelectedCommentRow(row);
    setIsCriteriaCommentModalOpen(true);
  };

  useEffect(() => {
    setFilteredTableData(
      tableData
        ? tableData.filter((item: any) => {
            const currentField = accessNestedProperty(
              item,
              selectedField.fieldValue,
            );
            if (isDefined(currentField)) {
              return currentField
                .toString()
                .toLowerCase()
                .includes(searchTerm.toLowerCase());
            }
            return true;
          })
        : [],
    );
  }, [searchTerm, selectedField.fieldValue, tableData]);

  const actionButton = () => {
    const formUrl = `${VUE_APP_BASE_URL}/forms/${challengeStatement?.formURL}`;

    const csvColumns = [
      ...columns.filter((item) => !hiddenColumnListForCSV.includes(item.name)),
      {
        name: 'What I liked',
        fieldValue: 'comments[0]',
      },
      {
        name: 'What concerned me',
        fieldValue: 'comments[1]',
      },
      {
        name: 'Follow-up questions (if any)',
        fieldValue: 'comments[2]',
      },
    ];

    const dataForCSV = [
      [`[${challengeCycle?.name ?? ''}] start-up selection report`],
      [],
      ['Result: Preferred start-up'],
      ...filteredTableData
        .filter((item: any) =>
          exportAdditionalData?.preferred_companies?.includes(item.id),
        )
        .map((item: any, index: number) => [
          `${index + 1}. ${item?.company?.name}`,
        ]),
      [],
      [],
      ['Start-up scoring'],
      [],
      ['S/N', ...csvColumns.map((item: any) => item.name)],
      ...filteredTableData
        .filter((item: any) =>
          challengeStatement?.selected_company_ids?.includes(item.id),
        )
        .map((company: any, index: number) => [
          index + 1,
          ...csvColumns.map((col: any) =>
            accessNestedProperty(company, col.fieldValue),
          ),
        ]),
      [],
      [],
      ['Q&A'],
      [],
      ...Object.entries(exportAdditionalData?.comments).flatMap(
        ([key, value]: any) => {
          const result = [
            [
              filteredTableData.find((item: any) => item.id === key)?.company
                ?.name,
            ],
          ];
          value.forEach((item: any) => {
            const firstName = item.createdBy?.nameFirstName ?? '';
            const lastName = item.createdBy?.nameLastName ?? '';

            const fullName = `${firstName} ${lastName}`;
            result.push([`${fullName}: ${item.commentText}`]);
          });
          result.push([]);
          return result;
        },
      ),
    ];

    return (
      <StyledButtonWrapper>
        <Button
          title="View evaluation form"
          size="medium"
          onClick={() => {
            window.open(formUrl, '_blank');
          }}
        />
        <StyledCSVLink
          data={dataForCSV}
          filename={`${challengeStatementName}.csv`}
        >
          <Button title="Export report" size="medium" />
        </StyledCSVLink>
      </StyledButtonWrapper>
    );
  };

  const table = (
    <>
      <StyledActionWrapper>
        <StyledSearchInput
          LeftIcon={IconSearch}
          placeholder={t`Search`}
          value={searchTerm}
          onChange={(value: string) => {
            setSearchTerm(value);
          }}
        />
        <StyledFilterWrapper>
          <CampaignsTableFilter
            columns={columns}
            selectedField={selectedField}
            setSelectedField={setSelectedField}
          />
        </StyledFilterWrapper>
      </StyledActionWrapper>

      <StyledEvaluatorSelect>
        <Select
          dropdownId={'evaluator-score'}
          dropdownWidthAuto
          value={selectedEvaluator}
          options={listEvaluatorOptions}
          onChange={setSelectedEvaluator}
        />
      </StyledEvaluatorSelect>

      <StyledTableContainer>
        <CampaignsTable
          columns={columns}
          data={filteredTableData.filter((item: any) =>
            challengeStatement?.selected_company_ids?.includes(item.id),
          )}
          selectedRows={selectedRows}
          setSelectedRows={setSelectedRows}
          selectAllStatus={selectAllStatus}
          setSelectAllStatus={setSelectAllStatus}
          loading={tableLoading}
        />
        {isCriteriaCommentModalOpen && (
          <CriteriaCommentModalWrapper
            onClose={() => setIsCriteriaCommentModalOpen(false)}
            currentStatement={challengeStatement}
            currentRow={selectedCommentRow}
          />
        )}
        {isCommentModalOpen && (
          <CommentModalWrapper
            onClose={() => {
              setIsCommentModalOpen(false);
              setRefetchAdditionalComment((prev) => !prev);
            }}
            currentStatement={challengeStatement}
            currentRow={selectedCommentRow}
          />
        )}
      </StyledTableContainer>
    </>
  );

  const tableWrapper = (children: React.ReactNode) => (
    <SubMenuTopBarContainer
      links={[
        {
          children: <Trans>Campaigns</Trans>,
        },
        {
          children: challengeStatement?.challengeCycleName ?? '',
          href: getCampaignsPath(CampaignsPath.ChallengeCycleDetail, {
            id: challengeStatement?.challengeCycleId ?? '',
          }),
        },
        {
          children: <Trans>Evaluation Results</Trans>,
          href: getCampaignsPath(CampaignsPath.EvaluationResultDetail, {
            id: challengeStatement?.challengeCycleId ?? '',
          }),
        },
        {
          children: `Stage ${currentStage === EVALUATION_RESULTS_STAGE.STAGE_2 ? '2' : '3'} Results`,
          href: getCampaignsPath(CampaignsPath.EvaluationResultDetail, {
            id: challengeStatement?.challengeCycleId ?? '',
          }),
        },
        {
          children: challengeStatement?.key
            ? formatStatementKey(challengeStatement?.key)
            : '',
        },
      ]}
    >
      <CampaignsPageContainer>
        <Section>
          <CampaignsBackButton
            to={getCampaignsPath(
              CampaignsPath.EvaluationResultDetail,
              {
                id: challengeStatement?.challengeCycleId ?? '',
              },
              { stage: currentStage },
            )}
          />
          <UndecoratedLink
            to={getAppPath(AppPath.RecordShowPage, {
              objectNameSingular: CoreObjectNameSingular.ChallengeStatement,
              objectRecordId: challengeStatement?.id ?? '',
            })}
          >
            <StyledTitle>
              {challengeStatementName}
              <IconLink />
            </StyledTitle>
          </UndecoratedLink>
          <StyledTableText>{challengeStatement?.detail}</StyledTableText>
          {children}
        </Section>
      </CampaignsPageContainer>
    </SubMenuTopBarContainer>
  );

  return {
    tableWrapper,
    table,
    actionButton,
  };
};
