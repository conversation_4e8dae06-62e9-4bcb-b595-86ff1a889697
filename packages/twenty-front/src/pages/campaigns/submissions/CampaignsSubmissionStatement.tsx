import useChallengeCycle from '@/api/hooks/useChallengeCycle';
import useEvaluationSubmission from '@/api/hooks/useEvaluationSubmission';
import { currentWorkspaceMemberState } from '@/auth/states/currentWorkspaceMemberState';
import { CoreObjectNameSingular } from '@/object-metadata/types/CoreObjectNameSingular';
import { AppPath } from '@/types/AppPath';
import { CampaignsPath } from '@/types/CampaignsPath';
import { useDialogManager } from '@/ui/feedback/dialog-manager/hooks/useDialogManager';
import { SnackBarVariant } from '@/ui/feedback/snack-bar-manager/components/SnackBar';
import { useSnackBar } from '@/ui/feedback/snack-bar-manager/hooks/useSnackBar';
import { Select } from '@/ui/input/components/Select';
import { TextInput } from '@/ui/input/components/TextInput';
import { SubMenuTopBarContainer } from '@/ui/layout/page/components/SubMenuTopBarContainer';
import { useTheme } from '@emotion/react';
import styled from '@emotion/styled';
import { t } from '@lingui/core/macro';
import { Trans } from '@lingui/react/macro';
import { useEffect, useMemo, useState } from 'react';
import { useParams } from 'react-router-dom';
import { useRecoilValue } from 'recoil';
import {
  Button,
  IconLink,
  IconMenu2,
  IconPlus,
  IconSearch,
  IconTrash,
  isDefined,
  RawLink,
  Section,
  Tag,
  TagColor,
  UndecoratedLink,
} from 'twenty-ui';
import { useDebouncedCallback } from 'use-debounce';
import { CampaignsBackButton } from '~/pages/campaigns/components/CampaignsBackButton';
import { CampaignsPageContainer } from '~/pages/campaigns/components/CampaignsPageContainer';
import { CampaignsTable } from '~/pages/campaigns/components/table/CampaignsTable';
import { CampaignsTablePagination } from '~/pages/campaigns/components/table/CampaignsTablePagination';
import { CampaignsTableTagCell } from '~/pages/campaigns/components/table/cells/CampaignsTableTagCell';
import { CampaignsTableTextCell } from '~/pages/campaigns/components/table/cells/CampaignsTableTextCell';
import { ChallengeCycle } from '~/pages/campaigns/components/types/ChallengeCycle';
import { ChallengeStatement } from '~/pages/campaigns/components/types/ChallengeStatement';
import {
  COMPANY_GRADING,
  COMPANY_GRADING_OPTIONS,
} from '~/pages/campaigns/components/types/CompanyGradingType';
import { MoveCompanyModalWrapper } from '~/pages/campaigns/submissions/components/MoveCompanyModalWrapper';
import {
  COMPANY_STATUS,
  COMPANY_STATUS_OPTIONS,
} from '~/pages/campaigns/submissions/types/CampaignsSubmissionsType';
import { revertEnumValue } from '~/pages/campaigns/utils/getEnumValue';
import {
  ACTION_CODE,
  PERMISSION_CODE,
  useHasPermission,
} from '~/pages/settings/hooks/useHasPermission';
import { formatStatementKey } from '~/utils/format/formatStatementKey';
import { getAppPath } from '~/utils/navigation/getAppPath';
import { getCampaignsPath } from '~/utils/navigation/getCampaignsPath';

const StyledTitle = styled.div`
  align-items: center;
  color: ${({ theme }) => theme.font.color.primary};
  display: flex;
  font-size: ${({ theme }) => theme.font.size.xxl};
  font-weight: ${({ theme }) => theme.font.weight.semiBold};
  margin: ${({ theme }) => theme.spacing(3)} 0;
  gap: ${({ theme }) => theme.spacing(2)};
  width: fit-content;
`;

const StyledTableText = styled.div`
  margin-bottom: ${({ theme }) => theme.spacing(3)};
  margin-top: ${({ theme }) => theme.spacing(4)};
`;

const StyledSearchInput = styled(TextInput)`
  padding-bottom: ${({ theme }) => theme.spacing(2)};
  width: 100%;
`;

// eslint-disable-next-line @nx/workspace-no-hardcoded-colors
const StyledLinkText = styled.div`
  color: #295eff;
  text-decoration: underline;
`;

const StyledTableContainer = styled.div`
  align-items: center;
  display: flex;
  flex-direction: column;
  height: 100%;
  justify-content: space-between;
`;

const StyledAddButtonWrapper = styled.div`
  position: fixed;
  bottom: 30px;
  background-color: ${({ theme }) => theme.background.primary};
  margin-top: ${({ theme }) => theme.spacing(6)};
  align-items: center;
  display: flex;
  gap: ${({ theme }) => theme.spacing(2)};
  justify-content: center;
  border: 1px solid ${({ theme }) => theme.color.gray30};
  border-radius: ${({ theme }) => theme.border.radius.sm};
  padding: ${({ theme }) => theme.spacing(2)} ${({ theme }) => theme.spacing(4)};
  width: fit-content;
`;

const StyledAddButton = styled.div`
  border: 1px solid ${({ theme }) => theme.color.gray30};
  align-items: center;
  border-radius: ${({ theme }) => theme.border.radius.sm};
  color: ${({ theme }) => theme.font.color.primary};
  cursor: pointer;
  display: flex;
  font-size: ${({ theme }) => theme.font.size.md};
  font-weight: ${({ theme }) => theme.font.weight.semiBold};
  gap: ${({ theme }) => theme.spacing(1)};
  padding: ${({ theme }) => theme.spacing(1)} ${({ theme }) => theme.spacing(2)};
  text-align: center;
`;

export const CampaignsSubmissionStatement = () => {
  const {
    getChallengeCycle,
    getChallengeStatement,
    getChallengeStatementCompanies,
    updateChallengeStatementCompanyDetail,
    deleteMultipleChallengeStatementCompanies,
  } = useChallengeCycle();
  const {
    getListMultipleSubmissions,
    createEvaluationSubmissions,
    updateEvaluationSubmissions,
  } = useEvaluationSubmission();
  const theme = useTheme();
  const { enqueueSnackBar } = useSnackBar();
  const { enqueueDialog } = useDialogManager();

  const pathParams = useParams<{ id: string }>();
  const currentWorkspaceMember = useRecoilValue(currentWorkspaceMemberState);

  const [challengeCycle, setChallengeCycle] = useState<ChallengeCycle | null>(
    null,
  );
  const [challengeStatement, setChallengeStatement] =
    useState<ChallengeStatement | null>(null);
  const [hasCompanyData, setHasCompanyData] = useState<boolean>(false);
  const [tableData, setTableData] = useState<any[]>([]);
  const [selectedRows, setSelectedRows] = useState<Record<number, boolean>>({});
  const [selectAllStatus, setSelectAllStatus] = useState<
    'all' | 'some' | 'none'
  >('none');
  const [isMoveCompanyModalOpen, setIsMoveCompanyModalOpen] =
    useState<boolean>(false);

  const [getCompanies, setGetCompanies] = useState<boolean>(false);
  const [gradingColumnIdList, setGradingColumnIdList] = useState<string[]>([]);

  const hideColumn = !gradingColumnIdList.includes(
    currentWorkspaceMember?.id ?? '',
  );

  const [searchTerm, setSearchTerm] = useState<string>('');
  const [searchFilter, setSearchFilter] = useState<string>('');
  const [currentPage, setCurrentPage] = useState(1);
  const [itemsPerPage, setItemsPerPage] = useState(10);
  const [totalRecord, setTotalRecord] = useState<number>(0);

  const debouncedSetFilter = useDebouncedCallback((value) => {
    setCurrentPage(1);
    setSearchFilter(value);
  }, 500);

  const hasEditPermission = useHasPermission(
    PERMISSION_CODE.CHALLENGE_CYCLE_ENTITY,
    ACTION_CODE.EDIT,
  );

  const columns = useMemo(
    () => [
      {
        name: 'Company',
        width: 200,
        selector: (row: any) => (
          <UndecoratedLink
            to={getCampaignsPath(CampaignsPath.SubmissionStatementCompany, {
              id: row.id ? row.id : '',
            })}
            fullWidth
          >
            <CampaignsTableTagCell value={row.company.name} />
          </UndecoratedLink>
        ),
      },
      {
        name: 'Country of Incorporation',
        width: hideColumn ? '' : 158,
        selector: (row: any) =>
          row.company.incorporationCountry
            ? revertEnumValue(row.company.incorporationCountry)
            : '',
      },
      {
        name: 'Recommended by',
        width: hideColumn ? '' : 115,
        selector: (row: any) => (
          <CampaignsTableTagCell value={row.recommendedByName} />
        ),
      },
      {
        name: 'Key Offering',
        width: hideColumn ? '' : 80,
        selector: (row: any) =>
          row.onePagerURLSigned && (
            <RawLink href={row.onePagerURLSigned || ''}>
              <StyledLinkText>Link here</StyledLinkText>
            </RawLink>
          ),
      },
      {
        name: 'Company Status',
        width: hideColumn ? '' : 210,
        noPadding: hasEditPermission,
        selector: (row: any) =>
          hasEditPermission ? (
            <Select
              dropdownId={`drop-down-id-status-${row.id}`}
              dropdownWidthAuto
              value={row.status ?? COMPANY_STATUS.GATHERING_REQUIREMENTS}
              emptyOption={{ label: 'Select', value: '' }}
              options={Object.entries(COMPANY_STATUS_OPTIONS).map(
                ([key, value]) => ({
                  label: value.label,
                  value: key,
                  color: value.color,
                }),
              )}
              onChange={(value) => setStatusValue(value, row.id)}
              fullWidth
              isTableSelect={true}
              type="tag"
            />
          ) : (
            COMPANY_STATUS_OPTIONS[row.status]?.label && (
              <Tag
                color={COMPANY_STATUS_OPTIONS[row.status]?.color as TagColor}
                text={COMPANY_STATUS_OPTIONS[row.status]?.label}
              />
            )
          ),
      },
      {
        name: 'Grading',
        width: 240,
        hidden: false,
        noPadding: hasEditPermission,
        selector: (row: any) =>
          hasEditPermission ? (
            <Select
              dropdownId={`drop-down-id-grading-${row.id}`}
              dropdownWidthAuto
              value={row.grade ?? COMPANY_GRADING.TO_BE_GRADED}
              emptyOption={{ label: 'Select', value: '' }}
              options={Object.entries(COMPANY_GRADING_OPTIONS).map(
                ([key, value]) => ({
                  label: value.label,
                  value: key,
                  color: value.color,
                }),
              )}
              onChange={(value) =>
                setGradingValue(value, row.id, row.evaluationId)
              }
              fullWidth
              isTableSelect={true}
              type="tag"
              disabled={
                row.status !== COMPANY_STATUS.READY_FOR_EVALUATION_STAGE_1
              }
            />
          ) : (
            COMPANY_GRADING_OPTIONS[row.grade]?.label && (
              <Tag
                color={COMPANY_GRADING_OPTIONS[row.grade]?.color as TagColor}
                text={COMPANY_GRADING_OPTIONS[row.grade]?.label}
              />
            )
          ),
      },
      {
        name: 'Comments',
        width: 240,
        hidden: hideColumn,
        noPadding: true,
        selector: (row: any) => (
          <CampaignsTableTextCell
            value={row.comment ?? ''}
            onChange={(value) =>
              setCommentValue(value, row.id, row.evaluationId)
            }
            disabled={
              row.status !== COMPANY_STATUS.READY_FOR_EVALUATION_STAGE_1
            }
          />
        ),
      },
    ],
    // eslint-disable-next-line react-hooks/exhaustive-deps
    [currentWorkspaceMember?.id, gradingColumnIdList],
  );

  useEffect(() => {
    const getChallengeStatementData = async (id: string) => {
      const res = await getChallengeStatement(id);
      if (isDefined(res)) {
        setChallengeStatement(res);
      }
    };
    if (isDefined(pathParams.id)) {
      getChallengeStatementData(pathParams.id);
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  useEffect(() => {
    if (!hasCompanyData) return;
    const getChallengeCycleData = async (id: string) => {
      const res = await getChallengeCycle(id);
      if (isDefined(res)) {
        setChallengeCycle(res);
        const listId = [...res.evaluators];
        setGradingColumnIdList(listId);
      }
    };
    const getSubmissionEvaluationData = async (id: string) => {
      const res = await getListMultipleSubmissions({
        challengeStatementId: id,
        internalEvaluatorId: currentWorkspaceMember?.id,
      });
      if (isDefined(res.data.submissions)) {
        const submissionMap = new Map<string, any>(
          res.data.submissions.map((item: any) => [
            item.challengeStatementCompanyId,
            item,
          ]),
        );
        setTableData((prev) => {
          return prev.map((company) => {
            const currentSubmission = submissionMap.get(company.id);
            if (isDefined(currentSubmission)) {
              return {
                ...company,
                grade: currentSubmission.evaluation?._internal?.grade ?? '',
                comment: currentSubmission.evaluation?._internal?.comment ?? '',
                evaluationId: currentSubmission.id,
              };
            }
            return company;
          });
        });
      }
    };
    if (isDefined(challengeStatement?.challengeCycleId)) {
      getChallengeCycleData(challengeStatement.challengeCycleId);
    }
    if (isDefined(challengeStatement?.id)) {
      getSubmissionEvaluationData(challengeStatement.id);
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [challengeStatement, hasCompanyData]);

  useEffect(() => {
    const getChallengeStatementCompanyData = async (id: string) => {
      const res = await getChallengeStatementCompanies({
        challengeStatementId: id,
        filter: JSON.stringify({ companyName: searchFilter }),
        sort: JSON.stringify([['updatedAt', 'desc']]),
        page: currentPage,
        page_size: itemsPerPage,
      });
      if (isDefined(res.data)) {
        setTableData(res.data.items);
        setTotalRecord(res.data.total);
        setHasCompanyData(true);
      }
    };
    if (isDefined(pathParams.id)) {
      getChallengeStatementCompanyData(pathParams.id);
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [getCompanies, currentPage, itemsPerPage, searchFilter]);

  const setStatusValue = async (value: string, id: string) => {
    const resUpdate = await updateChallengeStatementCompanyDetail(id, {
      status: value,
    });
    if (isDefined(resUpdate)) {
      setSelectValue(value, id, 'status');
    }
  };

  const setGradingValue = async (
    value: string,
    id: string,
    evaluationId: string,
  ) => {
    try {
      if (isDefined(evaluationId)) {
        await updateEvaluation(value, evaluationId, 'grade');
      } else {
        await createEvaluation(value, id, 'grade');
      }
      setSelectValue(value, id, 'grade');
    } catch (err) {
      enqueueSnackBar(
        `${isDefined(evaluationId) ? 'Update' : 'Create'} evaluation submissions failed.`,
        { variant: SnackBarVariant.Error },
      );
    }
  };

  const setCommentValue = useDebouncedCallback(
    async (value: string, id: string, evaluationId: string) => {
      try {
        if (isDefined(evaluationId)) {
          await updateEvaluation(value, evaluationId, 'comment');
        } else {
          await createEvaluation(value, id, 'comment');
        }
      } catch (err) {
        enqueueSnackBar(
          `${isDefined(evaluationId) ? 'Update' : 'Create'} evaluation submissions failed.`,
          { variant: SnackBarVariant.Error },
        );
        setSelectValue('', id, 'comment');
      }
    },
    3000,
  );

  const createEvaluation = async (value: string, id: string, type: string) => {
    const internalValue = {
      [type]: value,
    };
    if (type === 'comment') {
      internalValue.grade = COMPANY_GRADING.TO_BE_GRADED;
    }
    const createRes = await createEvaluationSubmissions({
      evaluations: [
        {
          challengeStatementCompanyId: id,
          challengeStatementId: challengeStatement?.id,
          stageId: challengeCycle?.evaluationStages[0].id,
          evaluation: {
            _internal: internalValue,
          },
          internalEvaluatorId: currentWorkspaceMember?.id,
          externalEvaluatorEmail: null,
        },
      ],
    });
    if (isDefined(createRes) && isDefined(createRes.data[0])) {
      setSelectValue(createRes.data[0].id, id, 'evaluationId');
    }
  };

  const updateEvaluation = async (
    value: string,
    evaluationId: string,
    type: string,
  ) => {
    await updateEvaluationSubmissions(evaluationId, {
      evaluation: {
        _internal: {
          [type]: value,
        },
      },
    });
  };

  const setSelectValue = (value: string, id: string, type: string) => {
    setTableData((prev) =>
      prev.map((item: any) =>
        item.id === id ? { ...item, [type]: value } : item,
      ),
    );
  };

  const handleDeleteRecords = async () => {
    const idsToDelete = Object.keys(selectedRows);
    try {
      await deleteMultipleChallengeStatementCompanies(idsToDelete);
      setGetCompanies((prev) => !prev);
      setSelectedRows({});
      setSelectAllStatus('none');
    } catch (err: any) {
      enqueueSnackBar(
        err?.response?.data?.detail?.message || `Delete challenge cycle failed`,
        {
          variant: SnackBarVariant.Error,
        },
      );
    }
  };

  const handleConfirmDelete = () => {
    enqueueDialog({
      title: 'Delete Company',
      message:
        'Are you sure you want to delete company in this challenge statement? This action cannot be undone.',
      buttons: [
        { title: 'Cancel' },
        {
          title: 'Delete',
          onClick: () => {
            handleDeleteRecords();
          },
          accent: 'danger',
          role: 'confirm',
        },
      ],
    });
  };

  return (
    <SubMenuTopBarContainer
      actionButton={
        hasEditPermission && (
          <UndecoratedLink
            to={getCampaignsPath(CampaignsPath.SubmissionStatementAddCompany, {
              id: pathParams.id ?? '',
            })}
          >
            <Button Icon={IconPlus} title={t`Add company`} size="small" />
          </UndecoratedLink>
        )
      }
      links={[
        {
          children: <Trans>Campaigns</Trans>,
        },
        {
          children: <Trans>Submissions</Trans>,
          href: getCampaignsPath(CampaignsPath.Submissions),
        },
        {
          children: challengeStatement?.challengeCycleName ?? '',
          href: getCampaignsPath(CampaignsPath.SubmissionDetail, {
            id: challengeStatement?.challengeCycleId ?? '',
          }),
        },
        {
          children: challengeStatement?.statement,
        },
      ]}
    >
      <CampaignsPageContainer>
        <Section>
          <CampaignsBackButton
            to={getCampaignsPath(CampaignsPath.SubmissionDetail, {
              id: challengeStatement?.challengeCycleId ?? '',
            })}
          />
          <UndecoratedLink
            to={getAppPath(AppPath.RecordShowPage, {
              objectNameSingular: CoreObjectNameSingular.ChallengeStatement,
              objectRecordId: challengeStatement?.id ?? '',
            })}
          >
            <StyledTitle>
              {challengeStatement?.key && challengeStatement?.statement
                ? formatStatementKey(challengeStatement?.key) +
                  ': ' +
                  challengeStatement?.statement
                : ''}
              <IconLink />
            </StyledTitle>
          </UndecoratedLink>
          <StyledTableText>{challengeStatement?.detail}</StyledTableText>
          <StyledSearchInput
            LeftIcon={IconSearch}
            placeholder={t`Search`}
            value={searchTerm}
            onChange={(value: string) => {
              setSearchTerm(value);
              debouncedSetFilter(value);
            }}
          />

          <StyledTableContainer>
            <CampaignsTable
              columns={columns}
              data={tableData}
              selectedRows={selectedRows}
              setSelectedRows={setSelectedRows}
              selectAllStatus={selectAllStatus}
              setSelectAllStatus={setSelectAllStatus}
            />
            <CampaignsTablePagination
              currentPage={currentPage}
              itemsPerPage={itemsPerPage}
              totalItems={totalRecord}
              onPageChange={setCurrentPage}
              onItemsPerPageChange={setItemsPerPage}
            />
            {Object.keys(selectedRows).length > 0 && hasEditPermission && (
              <StyledAddButtonWrapper>
                {Object.keys(selectedRows).length} selected:
                <StyledAddButton onClick={handleConfirmDelete}>
                  <IconTrash size={theme.icon.size.md} />
                  Delete
                </StyledAddButton>
                <StyledAddButton
                  onClick={() => setIsMoveCompanyModalOpen(true)}
                >
                  <IconMenu2 size={theme.icon.size.md} />
                  Move company
                </StyledAddButton>
              </StyledAddButtonWrapper>
            )}
            {isMoveCompanyModalOpen && (
              <MoveCompanyModalWrapper
                onClose={() => setIsMoveCompanyModalOpen(false)}
                currentStatement={challengeStatement}
                selectedRows={selectedRows}
                setSelectedRows={setSelectedRows}
                setSelectAllStatus={setSelectAllStatus}
                setGetCompanies={setGetCompanies}
              />
            )}
          </StyledTableContainer>
        </Section>
      </CampaignsPageContainer>
    </SubMenuTopBarContainer>
  );
};
