import {
  Body,
  Controller,
  Get,
  Param,
  Post,
  Req,
  Res,
  UseFilters,
  UseGuards,
} from '@nestjs/common';

import { Response } from 'express';

import {
  FileStorageException,
  FileStorageExceptionCode,
} from 'src/engine/core-modules/file-storage/interfaces/file-storage-exception';

import {
  FileException,
  FileExceptionCode,
} from 'src/engine/core-modules/file/file.exception';
import { FileApiExceptionFilter } from 'src/engine/core-modules/file/filters/file-api-exception.filter';
import { HondaFilePathGuard } from 'src/engine/core-modules/file/guards/honda-file-path-guard';
import { FileService } from 'src/engine/core-modules/file/services/file.service';
import { HondaFileService } from 'src/engine/core-modules/file/services/honda-file.service';

@Controller('honda-files')
@UseFilters(FileApiExceptionFilter)
export class HondaFileController {
  constructor(
    private readonly fileService: FileService,
    private readonly hondaFileService: HondaFileService,
  ) {}

  @Get('*')
  @UseGuards(HondaFilePathGuard)
  async getHondaFile(
    @Param() params: string[],
    @Res() res: Response,
    @Req() req: Request,
  ) {
    // For Honda files, the entire path is the file path (no separate folder/filename split)
    const fullFilePath = params[0] || '';

    const containerName = (req as any)?.containerName;

    if (!containerName) {
      throw new FileException(
        'Unauthorized: missing container name',
        FileExceptionCode.UNAUTHENTICATED,
      );
    }

    try {
      const fileStream = await this.fileService.getFileStreamFromContainer({
        folderPath: '', // Empty folder path since fullFilePath contains the complete path
        filename: fullFilePath, // Use the full path as filename
        containerName,
      });

      fileStream.on('error', () => {
        throw new FileException(
          'Error streaming file from storage',
          FileExceptionCode.INTERNAL_SERVER_ERROR,
        );
      });

      fileStream.pipe(res);
    } catch (error) {
      if (
        error instanceof FileStorageException &&
        error.code === FileStorageExceptionCode.FILE_NOT_FOUND
      ) {
        throw new FileException(
          'File not found',
          FileExceptionCode.FILE_NOT_FOUND,
        );
      }

      throw new FileException(
        `Error retrieving file: ${error.message}`,
        FileExceptionCode.INTERNAL_SERVER_ERROR,
      );
    }
  }

  @Post('generate-url')
  async generateHondaFileUrl(
    @Body()
    body: {
      filePath: string;
      containerName: string;
      expiresIn?: string;
    },
  ) {
    const url = await this.hondaFileService.generateHondaFileUrl({
      filePath: body.filePath,
      containerName: body.containerName,
      expiresIn: body.expiresIn || '7d',
    });

    return { url };
  }

  @Post('generate-multiple-urls')
  async generateMultipleHondaFileUrls(
    @Body()
    body: {
      files: Array<{
        filePath: string;
        containerName: string;
      }>;
      expiresIn?: string;
    },
  ) {
    const urls = await this.hondaFileService.generateMultipleHondaFileUrls({
      files: body.files,
      expiresIn: body.expiresIn || '7d',
    });

    return { urls };
  }
}
