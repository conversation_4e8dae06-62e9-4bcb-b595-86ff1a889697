import { Modu<PERSON> } from '@nestjs/common';

import { EnvironmentService } from 'src/engine/core-modules/environment/environment.service';
import { FilePathGuard } from 'src/engine/core-modules/file/guards/file-path-guard';
import { HondaFilePathGuard } from 'src/engine/core-modules/file/guards/honda-file-path-guard';
import { JwtModule } from 'src/engine/core-modules/jwt/jwt.module';

import { FileController } from './controllers/file.controller';
import { HondaFileController } from './controllers/honda-file.controller';
import { FileService } from './services/file.service';
import { HondaFileService } from './services/honda-file.service';

@Module({
  imports: [JwtModule],
  providers: [
    FileService,
    HondaFileService,
    EnvironmentService,
    FilePathGuard,
    HondaFilePathGuard,
  ],
  exports: [FileService, HondaFileService],
  controllers: [FileController, HondaFileController],
})
export class FileModule {}
