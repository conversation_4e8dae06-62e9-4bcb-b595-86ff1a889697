import { CanActivate, ExecutionContext, Injectable } from '@nestjs/common';

import { JwtWrapperService } from 'src/engine/core-modules/jwt/services/jwt-wrapper.service';

@Injectable()
export class HondaFilePathGuard implements CanActivate {
  constructor(private readonly jwtWrapperService: JwtWrapperService) {}

  async canActivate(context: ExecutionContext): Promise<boolean> {
    const request = context.switchToHttp().getRequest();
    const query = request.query;

    console.log('=============== Honda File Path Guard - Query:', query);

    if (!query || !query['token']) {
      console.log('=============== Honda File Path Guard - No token found');

      return false;
    }

    // First decode to see the payload
    const decodedPayload = await this.jwtWrapperService.decode(query['token'], {
      json: true,
    });

    console.log(
      '=============== Honda File Path Guard - Decoded payload:',
      decodedPayload,
    );

    try {
      const payload = await this.jwtWrapperService.verifyWorkspaceToken(
        query['token'],
        'HONDA_FILE',
      );

      console.log(
        '=============== Honda File Path Guard - Verified payload:',
        payload,
      );

      if (!payload.containerName) {
        console.log(
          '=============== Honda File Path Guard - No container name in payload',
        );

        return false;
      }
    } catch (error) {
      console.log(
        '=============== Honda File Path Guard - Verification error:',
        error.message,
      );

      return false;
    }

    const containerName = decodedPayload?.['containerName'];

    console.log(
      '=============== Honda File Path Guard - Container name:',
      containerName,
    );

    request.containerName = containerName;

    return true;
  }
}
