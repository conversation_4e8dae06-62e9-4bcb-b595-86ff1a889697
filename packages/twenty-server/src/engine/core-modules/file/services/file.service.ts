import { Injectable } from '@nestjs/common';

import { Stream } from 'stream';

import { EnvironmentService } from 'src/engine/core-modules/environment/environment.service';
import { FileStorageService } from 'src/engine/core-modules/file-storage/file-storage.service';
import { JwtWrapperService } from 'src/engine/core-modules/jwt/services/jwt-wrapper.service';

@Injectable()
export class FileService {
  constructor(
    private readonly jwtWrapperService: JwtWrapperService,
    private readonly fileStorageService: FileStorageService,
    private readonly environmentService: EnvironmentService,
  ) {}

  async getFileStream(
    folderPath: string,
    filename: string,
    workspaceId: string,
  ): Promise<Stream> {
    const workspaceFolderPath = `workspace-${workspaceId}/${folderPath}`;

    return await this.fileStorageService.read({
      folderPath: workspaceFolderPath,
      filename,
    });
  }

  async encodeFileToken(payloadToEncode: Record<string, any>) {
    const fileTokenExpiresIn = this.environmentService.get(
      'FILE_TOKEN_EXPIRES_IN',
    );
    const secret = this.jwtWrapperService.generateAppSecret(
      'FILE',
      payloadToEncode.workspaceId,
    );

    const signedPayload = this.jwtWrapperService.sign(
      {
        ...payloadToEncode,
      },
      {
        secret,
        expiresIn: fileTokenExpiresIn,
      },
    );

    return signedPayload;
  }

  async encodeFileTokenWithOptions(params: {
    payloadToEncode: Record<string, any>;
    expiresIn?: string;
    tokenType?: string;
  }) {
    const { payloadToEncode, expiresIn, tokenType = 'FILE' } = params;

    const fileTokenExpiresIn =
      expiresIn || this.environmentService.get('FILE_TOKEN_EXPIRES_IN');
    const secret = this.jwtWrapperService.generateAppSecret(
      tokenType as any,
      payloadToEncode.workspaceId || 'honda-server',
    );

    const signedPayload = this.jwtWrapperService.sign(
      {
        ...payloadToEncode,
      },
      {
        secret,
        expiresIn: fileTokenExpiresIn,
      },
    );

    return signedPayload;
  }

  async getFileStreamFromContainer(params: {
    folderPath: string;
    filename: string;
    containerName?: string;
    workspaceId?: string;
  }): Promise<Stream> {
    const { folderPath, filename, containerName, workspaceId } = params;

    if (containerName) {
      // For Honda files, we need to create a temporary Azure driver for the specific container
      const { BlobServiceClient } = await import('@azure/storage-blob');
      const connectionString = this.environmentService.get(
        'STORAGE_AZURE_CONNECTION_STRING',
      );

      if (!connectionString) {
        throw new Error('Azure connection string not configured');
      }

      const blobServiceClient =
        BlobServiceClient.fromConnectionString(connectionString);
      const containerClient =
        blobServiceClient.getContainerClient(containerName);
      const blobClient = containerClient.getBlobClient(filename);

      try {
        const downloadResponse = await blobClient.download();

        if (!downloadResponse.readableStreamBody) {
          throw new Error('Failed to get readable stream from blob');
        }

        // Convert Azure ReadableStream to Node.js Stream
        const { Readable } = await import('stream');

        return Readable.fromWeb(downloadResponse.readableStreamBody as any);
      } catch (error) {
        throw new Error(
          `Failed to read file from container ${containerName}: ${error.message}`,
        );
      }
    } else {
      // For regular workspace files
      const finalFolderPath = `workspace-${workspaceId}/${folderPath}`;

      return await this.fileStorageService.read({
        folderPath: finalFolderPath,
        filename,
      });
    }
  }
}
