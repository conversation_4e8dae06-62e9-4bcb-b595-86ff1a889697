import { Injectable } from '@nestjs/common';

import { EnvironmentService } from 'src/engine/core-modules/environment/environment.service';
import { FileService } from 'src/engine/core-modules/file/services/file.service';

@Injectable()
export class HondaFileService {
  constructor(
    private readonly fileService: FileService,
    private readonly environmentService: EnvironmentService,
  ) {}

  async generateHondaFileUrl(params: {
    filePath: string;
    containerName: string;
    expiresIn?: string;
    serverUrl?: string;
  }): Promise<string> {
    const { filePath, containerName, expiresIn = '7d', serverUrl } = params;

    const baseUrl = serverUrl || this.environmentService.get('SERVER_URL');

    const payloadToEncode = {
      containerName,
      filePath,
      type: 'HONDA_FILE',
      workspaceId: 'honda-server', // Ensure workspaceId is included
    };

    const signedPayload = await this.fileService.encodeFileTokenWithOptions({
      payloadToEncode,
      expiresIn,
      tokenType: 'HONDA_FILE',
    });

    const finalUrl = `${baseUrl}/honda-files/${filePath}?token=${signedPayload}`;

    return finalUrl;
  }

  async generateMultipleHondaFileUrls(params: {
    files: Array<{
      filePath: string;
      containerName: string;
    }>;
    expiresIn?: string;
    serverUrl?: string;
  }): Promise<Array<{ filePath: string; url: string }>> {
    const { files, expiresIn = '7d', serverUrl } = params;

    const results = await Promise.all(
      files.map(async (file) => ({
        filePath: file.filePath,
        url: await this.generateHondaFileUrl({
          filePath: file.filePath,
          containerName: file.containerName,
          expiresIn,
          serverUrl,
        }),
      })),
    );

    return results;
  }
}
